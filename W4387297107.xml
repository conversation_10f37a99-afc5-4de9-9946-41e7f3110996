<article dtd-version="1.3" article-type="research-article" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:ali="http://www.niso.org/schemas/ali/1.0/"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><processing-meta tagset-family="jats" table-model="xhtml" mathml-version="3.0" base-tagset="archiving"><restricted-by>pmc</restricted-by></processing-meta><front><journal-meta><journal-id journal-id-type="nlm-ta">J Pharmacokinet Pharmacodyn</journal-id><journal-id journal-id-type="iso-abbrev">J Pharmacokinet Pharmacodyn</journal-id><journal-title-group><journal-title>Journal of Pharmacokinetics and Pharmacodynamics</journal-title></journal-title-group><issn pub-type="ppub">1567-567X</issn><issn pub-type="epub">1573-8744</issn><publisher><publisher-name>Springer US</publisher-name><publisher-loc>New York</publisher-loc></publisher></journal-meta>
<article-meta><article-id pub-id-type="pmcid">11576657</article-id><article-id pub-id-type="pmid">37787918</article-id>
<article-id pub-id-type="publisher-id">9884</article-id><article-id pub-id-type="doi">10.1007/s10928-023-09884-6</article-id><article-categories><subj-group subj-group-type="heading"><subject>Original Paper</subject></subj-group></article-categories><title-group><article-title>Towards a platform quantitative systems pharmacology (QSP) model for preclinical to clinical translation of antibody drug conjugates (ADCs)</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Scheuher</surname><given-names>Bruna</given-names></name><xref rid="Aff1" ref-type="aff">1</xref><xref rid="Aff2" ref-type="aff">2</xref></contrib><contrib contrib-type="author"><name><surname>Ghusinga</surname><given-names>Khem Raj</given-names></name><xref rid="Aff1" ref-type="aff">1</xref></contrib><contrib contrib-type="author"><name><surname>McGirr</surname><given-names>Kimiko</given-names></name><xref rid="Aff1" ref-type="aff">1</xref></contrib><contrib contrib-type="author"><name><surname>Nowak</surname><given-names>Maksymilian</given-names></name><xref rid="Aff1" ref-type="aff">1</xref></contrib><contrib contrib-type="author"><name><surname>Panday</surname><given-names>Sheetal</given-names></name><xref rid="Aff1" ref-type="aff">1</xref></contrib><contrib contrib-type="author"><name><surname>Apgar</surname><given-names>Joshua</given-names></name><xref rid="Aff1" ref-type="aff">1</xref></contrib><contrib contrib-type="author"><name><surname>Subramanian</surname><given-names>Kalyanasundaram</given-names></name><xref rid="Aff1" ref-type="aff">1</xref><xref rid="Aff3" ref-type="aff">3</xref></contrib><contrib corresp="yes" contrib-type="author"><name><surname>Betts</surname><given-names>Alison</given-names></name><address><email><EMAIL></email></address><xref rid="Aff1" ref-type="aff">1</xref><xref rid="Aff2" ref-type="aff">2</xref></contrib><aff id="Aff1"><label>1</label><institution-wrap><institution-id institution-id-type="ROR">https://ror.org/00j3vdq65</institution-id><institution-id institution-id-type="GRID">grid.504129.b</institution-id><institution>Applied BioMath, </institution></institution-wrap>561 Virginia Road, Concord, MA 01742 USA </aff><aff id="Aff2"><label>2</label><institution-wrap><institution-id institution-id-type="GRID">grid.419849.9</institution-id><institution-id institution-id-type="ISNI">0000 0004 0447 7762</institution-id><institution>Present Address: DMPK and Modeling, </institution><institution>Takeda, </institution></institution-wrap>Boston, MA United States </aff><aff id="Aff3"><label>3</label>Differentia Bio, Pleasanton, California United States </aff></contrib-group><pub-date pub-type="epub"><day>3</day><month>10</month><year>2023</year></pub-date><pub-date pub-type="pmc-release"><day>3</day><month>10</month><year>2023</year></pub-date><pub-date pub-type="ppub"><year>2024</year></pub-date><volume>51</volume><issue>5</issue><fpage>429</fpage><lpage>447</lpage><history><date date-type="received"><day>12</day><month>12</month><year>2022</year></date><date date-type="accepted"><day>16</day><month>8</month><year>2023</year></date></history><permissions><copyright-statement>© The Author(s) 2023</copyright-statement><copyright-year>2023</copyright-year><license><ali:license_ref content-type="ccbylicense" specific-use="textmining">https://creativecommons.org/licenses/by/4.0/</ali:license_ref><license-p><bold>Open Access</bold> This article is licensed under a Creative Commons Attribution 4.0 International License, which permits use, sharing, adaptation, distribution and reproduction in any medium or format, as long as you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons licence, and indicate if changes were made. The images or other third party material in this article are included in the article's Creative Commons licence, unless indicated otherwise in a credit line to the material. If material is not included in the article's Creative Commons licence and your intended use is not permitted by statutory regulation or exceeds the permitted use, you will need to obtain permission directly from the copyright holder. To view a copy of this licence, visit <ext-link xlink:href="https://creativecommons.org/licenses/by/4.0/" ext-link-type="uri">http://creativecommons.org/licenses/by/4.0/</ext-link>.</license-p></license></permissions><abstract id="Abs1"><p id="Par1">A next generation multiscale quantitative systems pharmacology (QSP) model for antibody drug conjugates (ADCs) is presented, for preclinical to clinical translation of ADC efficacy. Two HER2 ADCs (trastuzumab-DM1 and trastuzumab-DXd) were used for model development, calibration, and validation. The model integrates drug specific experimental data including in vitro cellular disposition data, pharmacokinetic (PK) and tumor growth inhibition (TGI) data for T-DM1 and T-DXd, as well as system specific data such as properties of HER2, tumor growth rates, and volumes. The model incorporates mechanistic detail at the intracellular level, to account for different mechanisms of ADC processing and payload release. It describes the disposition of the ADC, antibody, and payload inside and outside of the tumor, including binding to off-tumor, on-target sinks. The resulting multiscale PK model predicts plasma and tumor concentrations of ADC and payload. Tumor payload concentrations predicted by the model were linked to a TGI model and used to describe responses following ADC administration to xenograft mice. The model was translated to humans and virtual clinical trial simulations were performed that successfully predicted progression free survival response for T-DM1 and T-DXd for the treatment of HER2+ metastatic breast cancer, including differential efficacy based upon HER2 expression status. In conclusion, the presented model is a step toward a platform QSP model and strategy for ADCs, integrating multiple types of data and knowledge to predict ADC efficacy. The model has potential application to facilitate ADC design, lead candidate selection, and clinical dosing schedule optimization.</p><sec><title>Supplementary Information</title><p>The online version contains supplementary material available at 10.1007/s10928-023-09884-6.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Antibody drug conjugate</kwd><kwd>Quantitative systems pharmacology</kwd><kwd>HER2</kwd><kwd>Kadcyla</kwd><kwd>Enhertu</kwd><kwd>Oncology</kwd></kwd-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100000002</institution-id><institution>National Institutes of Health</institution></institution-wrap></funding-source><award-id>1R44GM134790-01A1</award-id></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>© Springer Science+Business Media, LLC, part of Springer Nature 2024</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Introduction</title><p id="Par2">Antibody drug conjugates (ADCs) are a class of targeted therapies for cancer treatment that combine a specific antibody to a tumor antigen linked to a potent cytotoxic agent. The aim of this therapeutic is to target the cytotoxic drug to tumor cells, thus maximizing efficacy whilst minimizing systemic toxicity. ADCs are clinically validated, with currently 14 ADC drug approvals received worldwide for various solid and hematological malignancies [<xref rid="CR1" ref-type="bibr">1</xref>]. These ADCs deliver chemotherapeutic payloads with diverse mechanisms of action including microtubule inhibitors, DNA cross-linkers and topoisomerase (TOPO) inhibitors. Advancements in conjugation chemistry and linker technology have enabled site specific conjugation of payloads and more stable linkers [<xref rid="CR2" ref-type="bibr">2</xref>, <xref rid="CR3" ref-type="bibr">3</xref>]. Several ADCs have been launched with accelerated approval and have demonstrated transformative responses in the clinic that are broad and deep even in treatment of refractory cancers. In addition, certain tumor payloads can stimulate immune responses, either by direct stimulation of dendritic cell activation and maturation or by triggering immunogenic cell death. As a result, checkpoint inhibitors have emerged as an obvious combination partner for ADCs [<xref rid="CR4" ref-type="bibr">4</xref>].</p><p id="Par3">However, it has been a significant journey to get to this point, and first-generation ADCs were limited by several challenges in the clinic including severe adverse events and sub-optimal efficacy, unable to reach their efficacious doses before the onset of dose limiting toxicities (DLTs). This is partly because ADCs are not truly targeted; they distribute throughout the body and can be taken up into healthy tissues via target-mediated mechanisms (on-target binding to healthy tissues expressing the ADC target, or via binding to FcγRs), or target-independent mechanisms such as non-specific endocytosis [<xref rid="CR5" ref-type="bibr">5</xref>]. In addition, ADCs can have poor penetration into solid tumors, which restricts their efficacy [<xref rid="CR6" ref-type="bibr">6</xref>]. The future success and optimal design of ADCs requires an improved mechanistic understanding to better predict efficacy and toxicity. In this manuscript we have sought to use mechanistic modeling approaches to better predict efficacy, with a second manuscript focusing on ADC toxicity.</p><p id="Par4">Prediction of ADC efficacy through experimental methods alone can be laborious, expensive or even infeasible [<xref rid="CR7" ref-type="bibr">7</xref>]. Traditional approaches can be misleading, especially when considered in isolation. In vitro cytotoxicity assays provide a single point estimate of an ADC’s potency for a chosen cell line (i.e. IC<sub>50</sub> or IC<sub>90</sub>) but do not give any information on the dynamics of the response. There are relatively few reports establishing in vitro to in vivo correlations for ADCs [<xref rid="CR8" ref-type="bibr">8</xref>]. While in vivo mouse tumor xenograft models can be useful to describe efficacy of ADCs, these models are an extreme simplification of human cancer. Species-specific differences in target expression/distribution, ADC PK, tumor penetration, tumor growth rates and heterogeneity need to be considered when translating the results to the clinic. In addition, xenograft mice are more tolerant to ADC toxicities, and dose can simply be increased until efficacy is demonstrated [<xref rid="CR9" ref-type="bibr">9</xref>].</p><p id="Par5">The inherent complexity of ADCs lends itself well to the use of mathematical modeling and simulation, to map out the mechanism of action and to consider the impact of multiple variables. Mechanistic approaches, such as quantitative systems pharmacology (QSP) models, combine computational modeling and experimental data to examine the relationships between a drug, the biological system and the disease process [<xref rid="CR10" ref-type="bibr">10</xref>, <xref rid="CR11" ref-type="bibr">11</xref>]. Several mathematical models have been published for ADCs over recent years, evolving from empirical and semi-mechanistic PK/PD models, towards more mechanism-based models [<xref rid="CR7" ref-type="bibr">7</xref>]. These models have been used to describe in vitro assays, preclinical animal experiments, and clinical trials in humans [<xref rid="CR7" ref-type="bibr">7</xref>]. They have become an important tool to support design of ADCs, to enable preclinical to clinical translation, and facilitate more efficient and effective development of ADCs. However, the models have generally been developed for specific ADCs and there is a need for a more holistic model, with sufficient intracellular detail to describe the mechanism of action of all approved ADCs, incorporating the learnings from previous models. This needs to be accompanied by a strategy for preclinical to clinical translation of ADCs, integrating multiple data types and established knowledge to predict ADC efficacy.</p><p id="Par6">In this manuscript, a next generation multiscale QSP platform model for ADCs is presented, that takes preclinical data routinely collected during ADC discovery and development as input and performs a clinical translation of ADC efficacy. The model was developed, calibrated, and validated using data from the public domain on trastuzumab emtansine (T-DM1) and trastuzumab deruxtecan (T-DXd), two ADCs with the same HER2-targeting antibody (trastuzumab) but having different linker-payloads, intracellular mechanisms of action, and clinical responses. The model describes ADC administration and disposition, including payload release, mechanistic characterization of tumor uptake, binding, and intracellular processing of ADC and payload. A detailed intracellular model with a distinct endo/ lysosomal compartment, receptor recycling, and differentiated release of payload from non-cleavable or cleavable linkers is included. In addition, the model describes the competition between the ADC and the naked antibody for binding to the tumor target. The PK model incorporates physiological volumes for better translation across species and includes the binding of ADC and the naked antibody to the healthy tissue sinks. Tumor payload concentrations predicted by the model were linked to a model of TGI in xenograft mice and then translated to humans to predict outcome via virtual clinical trial simulations. The presented model is a step toward a platform QSP model and strategy for ADCs, integrating multiple types of data and knowledge to predict ADC efficacy.</p></sec><sec id="Sec2"><title>Methods</title><sec id="Sec3"><title>Model structure</title><sec id="Sec4"><title>In vitro cellular model</title><p id="Par7">The in vitro model was developed to describe the dynamics that occur at the cellular level in an in vitro culture system (Figure S1A). This model also represents the tumor compartment in the in vivo version of the model (Fig. <xref ref-type="fig" rid="Fig1">1</xref>—Tumor Cell Model). The state variables included in the model diagram are defined in Table S1a, the model parameters are included in Table S2a and the reactions and equations in Table S3a and b. The ADC is dosed into the media and can deconjugate in a single step (<inline-formula id="IEq1"><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{dec}$$\end{document}</tex-math><mml:math id="M2"><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi mathvariant="italic">dec</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq1.gif"/></alternatives></inline-formula>), losing its payload, to produce unconjugated antibody and free payload. The ADC and antibody (Ab) reversibly bind to HER2 receptors on the cell surface (<inline-formula id="IEq2"><alternatives><tex-math id="M3">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{on,Ab}$$\end{document}</tex-math><mml:math id="M4"><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi>o</mml:mi><mml:mi>n</mml:mi><mml:mo>,</mml:mo><mml:mi>A</mml:mi><mml:mi>b</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq2.gif"/></alternatives></inline-formula>, <inline-formula id="IEq3"><alternatives><tex-math id="M5">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{off,Ab}$$\end{document}</tex-math><mml:math id="M6"><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi>o</mml:mi><mml:mi>f</mml:mi><mml:mi>f</mml:mi><mml:mo>,</mml:mo><mml:mi>A</mml:mi><mml:mi>b</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq3.gif"/></alternatives></inline-formula>) and are internalized into the cells (<inline-formula id="IEq4"><alternatives><tex-math id="M7">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{endo,HER2}$$\end{document}</tex-math><mml:math id="M8"><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi>e</mml:mi><mml:mi>n</mml:mi><mml:mi>d</mml:mi><mml:mi>o</mml:mi><mml:mo>,</mml:mo><mml:mi>H</mml:mi><mml:mi>E</mml:mi><mml:mi>R</mml:mi><mml:mn>2</mml:mn></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq4.gif"/></alternatives></inline-formula>, <inline-formula id="IEq5"><alternatives><tex-math id="M9">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{endo, HER2:Ab}$$\end{document}</tex-math><mml:math id="M10"><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi>e</mml:mi><mml:mi>n</mml:mi><mml:mi>d</mml:mi><mml:mi>o</mml:mi><mml:mo>,</mml:mo><mml:mi>H</mml:mi><mml:mi>E</mml:mi><mml:mi>R</mml:mi><mml:mn>2</mml:mn><mml:mo>:</mml:mo><mml:mi>A</mml:mi><mml:mi>b</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq5.gif"/></alternatives></inline-formula>). The binding interaction is modeled as a monovalent interaction. It is assumed that HER2 receptors are at steady-state, and that the total number of HER2 receptors does not change with time. Intracellular bound ADC/Ab can either recycle back to the cell surface (<inline-formula id="IEq6"><alternatives><tex-math id="M11">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{rec,HER2} , k_{rec, HER2:Ab}$$\end{document}</tex-math><mml:math id="M12"><mml:mrow><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi>r</mml:mi><mml:mi>e</mml:mi><mml:mi>c</mml:mi><mml:mo>,</mml:mo><mml:mi>H</mml:mi><mml:mi>E</mml:mi><mml:mi>R</mml:mi><mml:mn>2</mml:mn></mml:mrow></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi>r</mml:mi><mml:mi>e</mml:mi><mml:mi>c</mml:mi><mml:mo>,</mml:mo><mml:mi>H</mml:mi><mml:mi>E</mml:mi><mml:mi>R</mml:mi><mml:mn>2</mml:mn><mml:mo>:</mml:mo><mml:mi>A</mml:mi><mml:mi>b</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq6.gif"/></alternatives></inline-formula>) or degrade (<inline-formula id="IEq7"><alternatives><tex-math id="M13">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{deg,HER2}$$\end{document}</tex-math><mml:math id="M14"><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi>d</mml:mi><mml:mi>e</mml:mi><mml:mi>g</mml:mi><mml:mo>,</mml:mo><mml:mi>H</mml:mi><mml:mi>E</mml:mi><mml:mi>R</mml:mi><mml:mn>2</mml:mn></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq7.gif"/></alternatives></inline-formula>, <inline-formula id="IEq8"><alternatives><tex-math id="M15">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{deg,HER2:Ab}$$\end{document}</tex-math><mml:math id="M16"><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi>d</mml:mi><mml:mi>e</mml:mi><mml:mi>g</mml:mi><mml:mo>,</mml:mo><mml:mi>H</mml:mi><mml:mi>E</mml:mi><mml:mi>R</mml:mi><mml:mn>2</mml:mn><mml:mo>:</mml:mo><mml:mi>A</mml:mi><mml:mi>b</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq8.gif"/></alternatives></inline-formula>) in the endo/lysosomal compartment. Intracellular free payload is released following ADC degradation according to the drug to antibody ratio (DAR), and irreversibly escapes into the cytoplasm (<inline-formula id="IEq9"><alternatives><tex-math id="M17">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{in,PL}$$\end{document}</tex-math><mml:math id="M18"><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mi>n</mml:mi><mml:mo>,</mml:mo><mml:mi>P</mml:mi><mml:mi>L</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq9.gif"/></alternatives></inline-formula>) where it can reversibly bind to the intracellular payload target (<inline-formula id="IEq10"><alternatives><tex-math id="M19">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{on,PL, } k_{off,PL}$$\end{document}</tex-math><mml:math id="M20"><mml:mrow><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi>o</mml:mi><mml:mi>n</mml:mi><mml:mo>,</mml:mo><mml:mi>P</mml:mi><mml:mi>L</mml:mi><mml:mo>,</mml:mo></mml:mrow></mml:msub><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi>o</mml:mi><mml:mi>f</mml:mi><mml:mi>f</mml:mi><mml:mo>,</mml:mo><mml:mi>P</mml:mi><mml:mi>L</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq10.gif"/></alternatives></inline-formula>) or exit the cell (<inline-formula id="IEq11"><alternatives><tex-math id="M21">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{out,PL}$$\end{document}</tex-math><mml:math id="M22"><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi>o</mml:mi><mml:mi>u</mml:mi><mml:mi>t</mml:mi><mml:mo>,</mml:mo><mml:mi>P</mml:mi><mml:mi>L</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq11.gif"/></alternatives></inline-formula>). Free payload in the media can also (re-)enter the cell (<inline-formula id="IEq12"><alternatives><tex-math id="M23">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{in,PL}$$\end{document}</tex-math><mml:math id="M24"><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mi>n</mml:mi><mml:mo>,</mml:mo><mml:mi>P</mml:mi><mml:mi>L</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq12.gif"/></alternatives></inline-formula>). There is no first order elimination of ADC, only degradation to release payload. For T-DXd, an additional rate constant was included in the endosomal/ lysosomal compartment describing cleavage of the linker to release the payload (<inline-formula id="IEq13"><alternatives><tex-math id="M25">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{cleave}$$\end{document}</tex-math><mml:math id="M26"><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi mathvariant="italic">cleave</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq13.gif"/></alternatives></inline-formula>). Transport of released payload from the lysosome to the cytosol was described as a first order process. While this can capture the overall process, it is not mechanistically accurate and therefore a simplification for lys-smcc-DM1, which has been shown to be delivered from the lysosomes to the cytosol via an active transport protein [<xref rid="CR12" ref-type="bibr">12</xref>, <xref rid="CR13" ref-type="bibr">13</xref>]. Free payload exits the cell only by passive diffusion, with no active efflux required for DM1 or DXd.<fig id="Fig1"><label>Fig. 1</label><caption><p>Schematic representation of the ADC QSP model. The full QSP model consists of several submodels connected in a modular fashion. Submodels include <bold>a</bold> Tumor cell model, <bold>b</bold> Soluble target model, <bold>c</bold> Healthy cell model, <bold>d</bold> Tumor disposition model and <bold>e</bold> Tumor growth inhibition model. The full human model is shown. The in vitro cellular model (shown in Figure S1A) and the mouse model (shown in Figure S3) are simplifications of the human model. The model describes the disposition and PK of ADC, antibody and released payload in central, peripheral and tumor compartments. ADC is dosed in the central compartment, where it can deconjugate to release payload and naked antibody or distribute to peripheral and tumor compartments. The ADC and the Ab can bind to receptors expressed on healthy cells in the central and peripheral compartment, receptors expressed on tumor cells in the tumor compartment, and soluble receptors in central, peripheral, and tumor compartments. The ADC, antibody and payload are eliminated in central and peripheral compartments. The model incorporates a mechanistic characterization of tumor uptake, implemented using a Krogh cylinder model. In the tumor the ADC can deconjugate, ADC and Ab can bind to receptors on the surface of the tumor cell and be internalized into the endo/ lysosomal compartment. Here, intracellular bound ADC can either be recycled back to the cell surface, or the payload can be released either by linker cleavage (cleavable linkers) or via ADC degradation (non-cleavable linkers). Payload can escape into the cytosol and bind to its target or exit the cell. Free payload can also re-enter the cell. Tumor payload concentrations predicted by the model were linked to a model of TGI in xenograft mice. The state variables used in the model are described in Table S1, the model parameters are described in Table S2, and the reactions and resulting ordinary differential equations are shown in Table S3</p></caption><graphic id="MO1" xlink:href="10928_2023_9884_Fig1_HTML"/></fig></p><p id="Par8">To assist with parameterization, the model was calibrated to several in vitro datasets. Receptor-mediated cellular uptake was calibrated to a dataset describing incubation of <sup>125</sup>I-trastuzumab with SK-BR-3 breast cancer cells and measurement of cell surface, dissociated, internalized, and catabolized radioactivity [<xref rid="CR14" ref-type="bibr">14</xref>]. Internalization, recycling, and degradation rates were fitted using this data, as well as affinity of trastuzumab for HER2 and number of cells in the experiment. The model was validated using data from an independent study where trastuzumab was incubated with BT474 cancer cells and internalization of trastuzumab bound to HER2 was measured [<xref rid="CR15" ref-type="bibr">15</xref>]. These parameters were then used to model in vitro T-DM1 disposition experiments completed by Erickson et al<italic>.</italic>, whereby T-[H]<sup>3</sup>DM1 was incubated with BT-474EEI, SK-BR-3 and MCF7-neo/HER2 breast cancer cells [<xref rid="CR16" ref-type="bibr">16</xref>]. In these experiments, total, conjugated and unconjugated DM1 was measured inside cells, unconjugated DM1 was measured in the media and total DM1 metabolites were measured in the incubation. Parameters for the model were informed from calibration to the Austin et al<italic>.</italic> 2004 data, with estimation of extracellular T-DM1 deconjugation rate and numbers of receptors/cell for each cell line to match the intracellular concentrations reported in the data. Observed vs. predicted diagnostic plots for in vitro and in vivo fitting are provided in Figure S2. The final intracellular concentrations were calculated using the reported cellular volumes described in Table S2. In vitro disposition data was not available for T-DXd and its released payload deruxtecan. Since T-DM1 and T-DXd both have trastuzumab as their antibody backbone, the antibody parameters are shared between models. The payload specific parameters for DXd were extracted from the literature (see Table S2) and used directly in the model without calibration.</p></sec><sec id="Sec5"><title>In vivo mouse model development</title><sec id="Sec6"><title>Modeling plasma PK of ADC and payload in mouse and predicting tumor payload concentrations</title><p id="Par9">The mouse PK model combines mouse plasma PK, a mechanistic tumor uptake model and the cellular model described in Sect. 1 above, to predict ADC and payload concentration in the tumor (Figure S3). All of the state variables included in the model diagram are defined in Table S1b and model parameters are included in Table S2b. Trastuzumab does not bind to the rodent version of HER2 (ErbB2/neu) and therefore soluble target and normal tissue expression of target were not required in the mouse model [<xref rid="CR17" ref-type="bibr">17</xref>]. ADC is dosed into the central compartment and can distribute to peripheral tissues (<inline-formula id="IEq14"><alternatives><tex-math id="M27">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{12}$$\end{document}</tex-math><mml:math id="M28"><mml:msub><mml:mi>k</mml:mi><mml:mn>12</mml:mn></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq14.gif"/></alternatives></inline-formula>,<inline-formula id="IEq15"><alternatives><tex-math id="M29">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{21}$$\end{document}</tex-math><mml:math id="M30"><mml:msub><mml:mi>k</mml:mi><mml:mn>21</mml:mn></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq15.gif"/></alternatives></inline-formula>) or to the tumor. ADC can deconjugate (<inline-formula id="IEq16"><alternatives><tex-math id="M31">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{dec}$$\end{document}</tex-math><mml:math id="M32"><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi mathvariant="italic">dec</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq16.gif"/></alternatives></inline-formula>) in any compartment, releasing Ab and its payload (as a function of the DAR). Payload can also distribute to peripheral tissues (<inline-formula id="IEq17"><alternatives><tex-math id="M33">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{12,PL}$$\end{document}</tex-math><mml:math id="M34"><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mn>12</mml:mn><mml:mo>,</mml:mo><mml:mi>P</mml:mi><mml:mi>L</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq17.gif"/></alternatives></inline-formula>, <inline-formula id="IEq18"><alternatives><tex-math id="M35">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{21,PL}$$\end{document}</tex-math><mml:math id="M36"><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mn>21</mml:mn><mml:mo>,</mml:mo><mml:mi>P</mml:mi><mml:mi>L</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq18.gif"/></alternatives></inline-formula>). ADC, Ab, and payload are cleared by first order elimination (<inline-formula id="IEq19"><alternatives><tex-math id="M37">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{elim}$$\end{document}</tex-math><mml:math id="M38"><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi mathvariant="italic">elim</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq19.gif"/></alternatives></inline-formula><italic>)</italic> in central and peripheral compartments. Distribution of ADC, Ab, and payload from the central compartment into a solid tumor occurs through surface exchange <inline-formula id="IEq20"><alternatives><tex-math id="M39">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\left( {\frac{6*D}{{R_{tumor}^{2} }}} \right)$$\end{document}</tex-math><mml:math id="M40"><mml:mfenced open="(" close=")"><mml:mfrac><mml:mrow><mml:mn>6</mml:mn><mml:mrow/><mml:mo>∗</mml:mo><mml:mi>D</mml:mi></mml:mrow><mml:msubsup><mml:mi>R</mml:mi><mml:mrow><mml:mi mathvariant="italic">tumor</mml:mi></mml:mrow><mml:mn>2</mml:mn></mml:msubsup></mml:mfrac></mml:mfenced></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq20.gif"/></alternatives></inline-formula> when the tumor is small, and vascular exchange described using a Krogh cylinder model <inline-formula id="IEq21"><alternatives><tex-math id="M41">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\left( {\frac{{2*P*R_{cap} }}{{R_{krogh}^{2} }}} \right)$$\end{document}</tex-math><mml:math id="M42"><mml:mfenced open="(" close=")"><mml:mfrac><mml:mrow><mml:mn>2</mml:mn><mml:mrow/><mml:mo>∗</mml:mo><mml:mi>P</mml:mi><mml:mrow/><mml:mo>∗</mml:mo><mml:msub><mml:mi>R</mml:mi><mml:mrow><mml:mi mathvariant="italic">cap</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:msubsup><mml:mi>R</mml:mi><mml:mrow><mml:mi mathvariant="italic">krogh</mml:mi></mml:mrow><mml:mn>2</mml:mn></mml:msubsup></mml:mfrac></mml:mfenced></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq21.gif"/></alternatives></inline-formula> at larger tumor sizes [<xref rid="CR18" ref-type="bibr">18</xref>–<xref rid="CR20" ref-type="bibr">20</xref>]. The tumor void fraction (ε) represents the tumor volume that is accessible to ADC, Ab, or payload. There are different ε values for ADC/Ab and payload (based on MW). In the tumor extracellular environment, cellular disposition of ADC, Ab and payload are characterized using the parameters and model described in Sect. 1 (Figure S3—Tumor Cell Model). Physiological volumes were used for central, peripheral, and tumor compartments in mice [<xref rid="CR21" ref-type="bibr">21</xref>]. The T-DM1 and T-DXd mouse PK models were calibrated to plasma PK data in non-tumor bearing mice following IV administration at 3 mg/kg, with measurement of ADC and total antibody concentrations over time (Figure S4) [<xref rid="CR16" ref-type="bibr">16</xref>, <xref rid="CR22" ref-type="bibr">22</xref>]. The model was used to fit the data for both ADCs simultaneously. It was assumed that the ADC distribution and half-life parameters were the same for both T-DM1 and T-DXd, but the deconjugation rate was allowed to differ for each ADC. The model was also used to fit plasma PK data following IV administration of DXd at 1 mg/kg to non-tumor bearing mice, in order to estimate payload distribution and clearance rates [<xref rid="CR22" ref-type="bibr">22</xref>]. Comparable data was not available for T-DM1, however mouse PK data from IV administration of a maytansine with structural similarity to DM1 was available (100 µg/kg dose) which was used to estimate the payload half-life [<xref rid="CR23" ref-type="bibr">23</xref>]. For parsimony, distribution parameters were assumed to be the same for DM1 and DXd.</p><p id="Par10">The model was then used to simulate data from an in vivo tumor disposition study describing T-[H]<sup>3</sup>-DM1 administration at 300 µg/kg to BT-474EEI tumor-bearing xenograft mice with measurement of DM1 in both plasma and tumor [<xref rid="CR16" ref-type="bibr">16</xref>]. The reactions and equations for the integrated mouse PK and tumor uptake model are included in Table S3c and Table S3d.</p></sec><sec id="Sec7"><title>Modeling tumor growth inhibition in mouse</title><p id="Par11">Tumor growth and killing were implemented to capture ADC treatment response in tumor growth inhibition studies in xenograft mice across various human cell lines (Fig. <xref ref-type="fig" rid="Fig4">4</xref> and S5). As before, the state variables included in the model diagram are defined in Table S1b and model parameters are included in Table S2c. The TGI model used has been described previously [<xref rid="CR24" ref-type="bibr">24</xref>]. The tumor growth model describes both exponential tumor doubling time (<inline-formula id="IEq22"><alternatives><tex-math id="M43">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$t_{double}$$\end{document}</tex-math><mml:math id="M44"><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi mathvariant="italic">double</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq22.gif"/></alternatives></inline-formula>) and linear tumor growth (<inline-formula id="IEq23"><alternatives><tex-math id="M45">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{lin}$$\end{document}</tex-math><mml:math id="M46"><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi mathvariant="italic">lin</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq23.gif"/></alternatives></inline-formula>), limited by the maximum tumor volume (<inline-formula id="IEq24"><alternatives><tex-math id="M47">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$V_{tumor, max}$$\end{document}</tex-math><mml:math id="M48"><mml:msub><mml:mi>V</mml:mi><mml:mrow><mml:mi>t</mml:mi><mml:mi>u</mml:mi><mml:mi>m</mml:mi><mml:mi>o</mml:mi><mml:mi>r</mml:mi><mml:mo>,</mml:mo><mml:mi>m</mml:mi><mml:mi>a</mml:mi><mml:mi>x</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq24.gif"/></alternatives></inline-formula>). ψ is an arbitrary constant for switching from exponential to linear growth patterns set to a value of 20 [<xref rid="CR25" ref-type="bibr">25</xref>]. 4 tumor cell states (N1, N2, N3, N4) are included in the model, representing the proliferating cell mass (N1) and the terminally committed cells in a transduction cascade (N2 → N3 → N4). Tumor killing is described with a Michaelis–Menten type model acting on the proliferating cells (N1) and is dependent on the intracellular unconjugated tumor payload concentration. The total tumor volume (mm<sup>3</sup>) is the sum of all cells in the transduction cascade (N1 + N2 + N3 + N4). N2, N3 and N4 do not proliferate, but otherwise are similar to N1 in terms of receptor expression, binding, endocytosis, and recycling. The transduction cascade introduces a delay to the tumor cell killing (τ). The maximal killing rate (<inline-formula id="IEq25"><alternatives><tex-math id="M49">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{kill, max}$$\end{document}</tex-math><mml:math id="M50"><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi>k</mml:mi><mml:mi>i</mml:mi><mml:mi>l</mml:mi><mml:mi>l</mml:mi><mml:mo>,</mml:mo><mml:mi>m</mml:mi><mml:mi>a</mml:mi><mml:mi>x</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq25.gif"/></alternatives></inline-formula>) and concentration of payload at half-maximal kill rate (<inline-formula id="IEq26"><alternatives><tex-math id="M51">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$kc_{50}$$\end{document}</tex-math><mml:math id="M52"><mml:mrow><mml:mi>k</mml:mi><mml:msub><mml:mi>c</mml:mi><mml:mn>50</mml:mn></mml:msub></mml:mrow></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq26.gif"/></alternatives></inline-formula>) are estimated by fitting the model to mouse xenograft data.</p><p id="Par12">The mouse efficacy model was calibrated to TGI data for T-DM1 versus N87 [<xref rid="CR24" ref-type="bibr">24</xref>, <xref rid="CR26" ref-type="bibr">26</xref>], BT-474 [<xref rid="CR27" ref-type="bibr">27</xref>], BT474EEI [<xref rid="CR16" ref-type="bibr">16</xref>], and KPL-4 tumor cell lines [<xref rid="CR28" ref-type="bibr">28</xref>], and for T-DXd versus N87 tumor cell line [<xref rid="CR29" ref-type="bibr">29</xref>], following a range of doses. The reactions and equations for the mouse tumor growth inhibition model are included in Table S3c and Table S3d.</p></sec></sec><sec id="Sec8"><title>Human model development and analysis</title><sec id="Sec9"><title>Modeling the PK of ADC, total Ab, and payload in the plasma of cancer patients</title><p id="Par13">The human PK model captures the disposition and PK of the ADC, the Ab and the payload in the central, peripheral and tumor compartments in HER2+ cancer patients (Fig. <xref ref-type="fig" rid="Fig1">1</xref>). State variables included in the model diagram are defined in Table S1 and model parameters are included in Table S2d. ADC is dosed in the central compartment, where it can deconjugate or distribute to peripheral and tumor compartments as described in the mouse model. All compartments have human physiological volumes [<xref rid="CR21" ref-type="bibr">21</xref>]. In the human model, the ADC and the Ab can also bind to soluble HER2 receptors in central, peripheral, and tumor compartments. In addition, the ADC and the Ab can bind to HER2 receptors on healthy cells in the central and peripheral compartment as well as HER2 receptors expressed on tumor cells in the tumor compartment (<inline-formula id="IEq27"><alternatives><tex-math id="M53">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{on, Ab}$$\end{document}</tex-math><mml:math id="M54"><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi>o</mml:mi><mml:mi>n</mml:mi><mml:mo>,</mml:mo><mml:mi>A</mml:mi><mml:mi>b</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq27.gif"/></alternatives></inline-formula><italic>,</italic>
<inline-formula id="IEq28"><alternatives><tex-math id="M55">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{off, Ab}$$\end{document}</tex-math><mml:math id="M56"><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi>o</mml:mi><mml:mi>f</mml:mi><mml:mi>f</mml:mi><mml:mo>,</mml:mo><mml:mi>A</mml:mi><mml:mi>b</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq28.gif"/></alternatives></inline-formula>). HER2 receptors are shed (<inline-formula id="IEq29"><alternatives><tex-math id="M57">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{shed}$$\end{document}</tex-math><mml:math id="M58"><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi mathvariant="italic">shed</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq29.gif"/></alternatives></inline-formula>) to form soluble HER2 receptors and soluble HER2 receptor complexes with the ADC and the Ab, which then can also be degraded/eliminated (<inline-formula id="IEq30"><alternatives><tex-math id="M59">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$t_{{\raise.5ex\hbox{$\scriptstyle 1$}\kern-.1em/ \kern-.15em\lower.25ex\hbox{$\scriptstyle 2$} , sHER2}} , t_{{\raise.5ex\hbox{$\scriptstyle 1$}\kern-.1em/ \kern-.15em\lower.25ex\hbox{$\scriptstyle 2$} , sHER2:Ab}}$$\end{document}</tex-math><mml:math id="M60"><mml:mrow><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mpadded><mml:mn>1</mml:mn></mml:mpadded><mml:mspace width="-1.00006pt"/><mml:mo stretchy="false">/</mml:mo><mml:mspace width="-1.49994pt"/><mml:mpadded><mml:mn>2</mml:mn></mml:mpadded><mml:mo>,</mml:mo><mml:mi>s</mml:mi><mml:mi>H</mml:mi><mml:mi>E</mml:mi><mml:mi>R</mml:mi><mml:mn>2</mml:mn></mml:mrow></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mpadded><mml:mn>1</mml:mn></mml:mpadded><mml:mspace width="-1.00006pt"/><mml:mo stretchy="false">/</mml:mo><mml:mspace width="-1.49994pt"/><mml:mpadded><mml:mn>2</mml:mn></mml:mpadded><mml:mo>,</mml:mo><mml:mi>s</mml:mi><mml:mi>H</mml:mi><mml:mi>E</mml:mi><mml:mi>R</mml:mi><mml:mn>2</mml:mn><mml:mo>:</mml:mo><mml:mi>A</mml:mi><mml:mi>b</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq30.gif"/></alternatives></inline-formula>) [<xref rid="CR30" ref-type="bibr">30</xref>]. Soluble HER2 and its complexes can distribute between the central compartment and peripheral or tumor compartments. Soluble target shedding is linked to tumor size via membrane HER2 expression. The parameters for the ADC human PK model were informed from literature data or were carried through from the in vitro model fitting (see Table S2d). The human PK model was used to simulate ADC, total antibody and payload PK and compared to data from phase 1 clinical studies. For T-DM1, the model was used to simulate data following IV infusion to HER2+ metastatic breast cancer (MBC) patients at doses ranging from 0.3 to 4.8 mg/kg Q3W. ADC concentrations were measured during the first dose dosing cycle for all doses, and total antibody and released payload levels were reported at the MTD of 3.6 mg/kg [<xref rid="CR31" ref-type="bibr">31</xref>]. For T-DXd, the model was used to simulate data from patients with breast, gastric or gastro-oesophageal carcinomas, who received IV doses of T-DXd from 0.8 to 8.0 mg/kg Q3W. ADC levels were measured at all doses for 3 dosing cycles. In addition, total antibody and released deruxtecan were measured at 6.4 mg/kg over cycles 1–3 [<xref rid="CR32" ref-type="bibr">32</xref>].</p></sec><sec id="Sec10"><title>Predicting tumor growth inhibition in HER2+ metastatic breast cancer patients</title><p id="Par14">The calibrated human PK model for T-DM1 and T-DXd was used to predict tumor payload concentrations, and intracellular unconjugated tumor payload concentrations were linked to TGI as described for the mouse model (Table S2). The human model was parameterized for HER2+ MBC as follows: (a) clinically observed exponential and linear growth rates for HER2+ MBC were used in the human model [<xref rid="CR33" ref-type="bibr">33</xref>, <xref rid="CR34" ref-type="bibr">34</xref>], (b) HER2 expression levels of 20,000 and 1 million receptors per cell were simulated, approximating HER2 1+ and 3+ patients [<xref rid="CR35" ref-type="bibr">35</xref>] and (c) initial and maximal tumor burdens were set to clinically observed/ plausible values [<xref rid="CR36" ref-type="bibr">36</xref>, <xref rid="CR37" ref-type="bibr">37</xref>]. The drug effect parameters, including the transduction delay, maximum kill rate, and concentrations at half max kill rates were taken directly from mouse model estimations (Fig. <xref ref-type="fig" rid="Fig2">2</xref>). The translational strategy is illustrated in Fig. <xref ref-type="fig" rid="Fig2">2</xref>. Please refer to Table S2d for parameter values.<fig id="Fig2"><label>Fig. 2</label><caption><p>Workflow of ADC QSP model development and translational strategy, connecting in vitro, in vivo, and clinical data, models, and predictions. At each step, the model integrates the input data and knowledge of the biological mechanism to perform simulations and predictions which informs the next step in the strategy</p></caption><graphic id="MO3" xlink:href="10928_2023_9884_Fig2_HTML"/></fig></p></sec><sec id="Sec11"><title>PRCC and virtual clinical trial simulations</title><p id="Par15">Global sensitivity analysis was completed using a sampling-based method (latin hypercube partial rank correlation coefficient or LHS-PRCC) to identify the most sensitive parameters impacting the tumor volume response following ADC administration [<xref rid="CR38" ref-type="bibr">38</xref>]. Parameters were chosen for analysis based on parameter correlation and certainty of parameter values. Parameter ranges and distributions for PRCC sampling were determined by literature values or bounded ten-fold higher and lower of the mean (human parameter values resulting from T-DM1 PK calibration). Sampled parameter sets (n = 1000) were dosed with T-DM1 at 3.6 mg/kg Q3W four times and tumor volume AUC was the output used for determined partial correlation. This analysis was used to guide the selection of parameters to vary for the virtual clinical trial simulations.</p><p id="Par16">Using the results from the global sensitivity analysis, exponential doubling time (<inline-formula id="IEq31"><alternatives><tex-math id="M61">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$t_{double}$$\end{document}</tex-math><mml:math id="M62"><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi mathvariant="italic">double</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq31.gif"/></alternatives></inline-formula>), linear growth rate (<inline-formula id="IEq32"><alternatives><tex-math id="M63">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{lin}$$\end{document}</tex-math><mml:math id="M64"><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi mathvariant="italic">lin</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq32.gif"/></alternatives></inline-formula><italic>),</italic> maximal kill rate (<inline-formula id="IEq33"><alternatives><tex-math id="M65">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{kill, max}$$\end{document}</tex-math><mml:math id="M66"><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi>k</mml:mi><mml:mi>i</mml:mi><mml:mi>l</mml:mi><mml:mi>l</mml:mi><mml:mo>,</mml:mo><mml:mi>m</mml:mi><mml:mi>a</mml:mi><mml:mi>x</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq33.gif"/></alternatives></inline-formula>), and concentration at half maximal killing rate (<inline-formula id="IEq34"><alternatives><tex-math id="M67">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$kc_{50}$$\end{document}</tex-math><mml:math id="M68"><mml:mrow><mml:mi>k</mml:mi><mml:msub><mml:mi>c</mml:mi><mml:mn>50</mml:mn></mml:msub></mml:mrow></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq34.gif"/></alternatives></inline-formula>) were selected as the parameters to vary in the creation of the virtual population. The pharmacodynamic killing parameters were varied according to the range estimated from the mouse models (Table S2c), and clinical growth parameters were taken from the literature [<xref rid="CR33" ref-type="bibr">33</xref>, <xref rid="CR34" ref-type="bibr">34</xref>]. The virtual patient simulations for T-DM1 were completed to emulate the phase 2 trial by Burris et al.[<xref rid="CR39" ref-type="bibr">39</xref>]. Specifically, model simulations were performed for 95 HER2+ MBC patients administered 3.6 mg/kg T-DM1 every 3 weeks for 14 months. Patients with high (1e6) and low (2e4) HER2+ receptor expression were simulated and the results from each group were analyzed separately. The virtual patient simulations for T-DXd were completed to emulate a phase 2 trial [<xref rid="CR40" ref-type="bibr">40</xref>] and a phase 3 trial [<xref rid="CR41" ref-type="bibr">41</xref>]. To simulate the phase 2 trial, 184 HER2+ patients were administered T-DXd at 5.4 mg/kg Q3W for 20 months. To simulate the phase 3 trial, 373 patients who had low expression of HER2 were administered T-DXd at 5.4 mg/kg Q3W for 29 months. Coefficients of variation for each sampled parameter were calibrated to match the PD observed in the clinical trial data (Table S2e). While the coefficients of variation (CV) for <inline-formula id="IEq35"><alternatives><tex-math id="M69">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$t_{double}$$\end{document}</tex-math><mml:math id="M70"><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi mathvariant="italic">double</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq35.gif"/></alternatives></inline-formula><italic>,</italic>
<inline-formula id="IEq36"><alternatives><tex-math id="M71">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{lin}$$\end{document}</tex-math><mml:math id="M72"><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi mathvariant="italic">lin</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq36.gif"/></alternatives></inline-formula><italic>,</italic> and <inline-formula id="IEq37"><alternatives><tex-math id="M73">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{kill, max}$$\end{document}</tex-math><mml:math id="M74"><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi>k</mml:mi><mml:mi>i</mml:mi><mml:mi>l</mml:mi><mml:mi>l</mml:mi><mml:mo>,</mml:mo><mml:mi>m</mml:mi><mml:mi>a</mml:mi><mml:mi>x</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq37.gif"/></alternatives></inline-formula> were low, high <inline-formula id="IEq38"><alternatives><tex-math id="M75">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$kc_{50}$$\end{document}</tex-math><mml:math id="M76"><mml:mrow><mml:mi>k</mml:mi><mml:msub><mml:mi>c</mml:mi><mml:mn>50</mml:mn></mml:msub></mml:mrow></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq38.gif"/></alternatives></inline-formula> CVs were required for both ADCs to describe variability in efficacy observed.</p></sec></sec></sec><sec id="Sec12"><title>Progression free survival (PFS) predictions</title><p id="Par17">The virtual clinical trial simulations for T-DM1 and T-DXd in HER2+ MBC patients were used to predict PFS based on Response Evaluation Criteria In Solid Tumors (RECIST) guidelines [<xref rid="CR42" ref-type="bibr">42</xref>]. PFS values were calculated from simulated tumor diameters at different timepoints over the length of the trial. Responses were categorized as follows (i) complete response (tumor diameter &lt; 10 mm), (ii) partial response (&gt; 30% decrease in tumor diameter relative to baseline), (iii) stable disease (&lt; 30% decrease and &lt; 20% increase in tumor diameter from baseline), and iv) progressive disease (&gt; 20% increase in tumor diameter relative to baseline and absolute increase in tumor diameter of &gt; 5 mm). Observation of progressive disease at a given time point was defined as an event. Patient survival rate at each time interval was calculated using following as described by Rich and coworkers [<xref rid="CR43" ref-type="bibr">43</xref>]:<disp-formula id="Equa"><alternatives><tex-math id="M77">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$Survival \, rate_{t} = \frac{number\, of\, at-risk\, patients\, after\, event}{{number\, of\, at - risk\, patients\, during\, time\, interval}}$$\end{document}</tex-math><mml:math display="block" id="M78"><mml:mrow><mml:mi>S</mml:mi><mml:mi>u</mml:mi><mml:mi>r</mml:mi><mml:mi>v</mml:mi><mml:mi>i</mml:mi><mml:mi>v</mml:mi><mml:mi>a</mml:mi><mml:mi>l</mml:mi><mml:mspace width="0.166667em"/><mml:mi>r</mml:mi><mml:mi>a</mml:mi><mml:mi>t</mml:mi><mml:msub><mml:mi>e</mml:mi><mml:mi>t</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mi>n</mml:mi><mml:mi>u</mml:mi><mml:mi>m</mml:mi><mml:mi>b</mml:mi><mml:mi>e</mml:mi><mml:mi>r</mml:mi><mml:mspace width="0.166667em"/><mml:mi>o</mml:mi><mml:mi>f</mml:mi><mml:mspace width="0.166667em"/><mml:mi>a</mml:mi><mml:mi>t</mml:mi><mml:mo>-</mml:mo><mml:mi>r</mml:mi><mml:mi>i</mml:mi><mml:mi>s</mml:mi><mml:mi>k</mml:mi><mml:mspace width="0.166667em"/><mml:mi>p</mml:mi><mml:mi>a</mml:mi><mml:mi>t</mml:mi><mml:mi>i</mml:mi><mml:mi>e</mml:mi><mml:mi>n</mml:mi><mml:mi>t</mml:mi><mml:mi>s</mml:mi><mml:mspace width="0.166667em"/><mml:mi>a</mml:mi><mml:mi>f</mml:mi><mml:mi>t</mml:mi><mml:mi>e</mml:mi><mml:mi>r</mml:mi><mml:mspace width="0.166667em"/><mml:mi>e</mml:mi><mml:mi>v</mml:mi><mml:mi>e</mml:mi><mml:mi>n</mml:mi><mml:mi>t</mml:mi></mml:mrow><mml:mrow><mml:mi>n</mml:mi><mml:mi>u</mml:mi><mml:mi>m</mml:mi><mml:mi>b</mml:mi><mml:mi>e</mml:mi><mml:mi>r</mml:mi><mml:mspace width="0.166667em"/><mml:mi>o</mml:mi><mml:mi>f</mml:mi><mml:mspace width="0.166667em"/><mml:mi>a</mml:mi><mml:mi>t</mml:mi><mml:mo>-</mml:mo><mml:mi>r</mml:mi><mml:mi>i</mml:mi><mml:mi>s</mml:mi><mml:mi>k</mml:mi><mml:mspace width="0.166667em"/><mml:mi>p</mml:mi><mml:mi>a</mml:mi><mml:mi>t</mml:mi><mml:mi>i</mml:mi><mml:mi>e</mml:mi><mml:mi>n</mml:mi><mml:mi>t</mml:mi><mml:mi>s</mml:mi><mml:mspace width="0.166667em"/><mml:mi>d</mml:mi><mml:mi>u</mml:mi><mml:mi>r</mml:mi><mml:mi>i</mml:mi><mml:mi>n</mml:mi><mml:mi>g</mml:mi><mml:mspace width="0.166667em"/><mml:mi>t</mml:mi><mml:mi>i</mml:mi><mml:mi>m</mml:mi><mml:mi>e</mml:mi><mml:mspace width="0.166667em"/><mml:mi>i</mml:mi><mml:mi>n</mml:mi><mml:mi>t</mml:mi><mml:mi>e</mml:mi><mml:mi>r</mml:mi><mml:mi>v</mml:mi><mml:mi>a</mml:mi><mml:mi>l</mml:mi></mml:mrow></mml:mfrac></mml:mrow></mml:math><graphic position="anchor" xlink:href="10928_2023_9884_Article_Equa.gif"/></alternatives></disp-formula>where the time interval is defined by when an event occurred. PFS probability (%) was calculated as:<disp-formula id="Equb"><alternatives><tex-math id="M79">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$Survival\, probability \left( \% \right) = Survival\, rate_{t} * Survival\, rate_{t - 1}$$\end{document}</tex-math><mml:math display="block" id="M80"><mml:mrow><mml:mi>S</mml:mi><mml:mi>u</mml:mi><mml:mi>r</mml:mi><mml:mi>v</mml:mi><mml:mi>i</mml:mi><mml:mi>v</mml:mi><mml:mi>a</mml:mi><mml:mi>l</mml:mi><mml:mspace width="0.166667em"/><mml:mi>p</mml:mi><mml:mi>r</mml:mi><mml:mi>o</mml:mi><mml:mi>b</mml:mi><mml:mi>a</mml:mi><mml:mi>b</mml:mi><mml:mi>i</mml:mi><mml:mi>l</mml:mi><mml:mi>i</mml:mi><mml:mi>t</mml:mi><mml:mi>y</mml:mi><mml:mfenced open="(" close=")"><mml:mo>%</mml:mo></mml:mfenced><mml:mo>=</mml:mo><mml:mi>S</mml:mi><mml:mi>u</mml:mi><mml:mi>r</mml:mi><mml:mi>v</mml:mi><mml:mi>i</mml:mi><mml:mi>v</mml:mi><mml:mi>a</mml:mi><mml:mi>l</mml:mi><mml:mspace width="0.166667em"/><mml:mi>r</mml:mi><mml:mi>a</mml:mi><mml:mi>t</mml:mi><mml:msub><mml:mi>e</mml:mi><mml:mi>t</mml:mi></mml:msub><mml:mrow/><mml:mo>∗</mml:mo><mml:mi>S</mml:mi><mml:mi>u</mml:mi><mml:mi>r</mml:mi><mml:mi>v</mml:mi><mml:mi>i</mml:mi><mml:mi>v</mml:mi><mml:mi>a</mml:mi><mml:mi>l</mml:mi><mml:mspace width="0.166667em"/><mml:mi>r</mml:mi><mml:mi>a</mml:mi><mml:mi>t</mml:mi><mml:msub><mml:mi>e</mml:mi><mml:mrow><mml:mi>t</mml:mi><mml:mo>-</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math><graphic position="anchor" xlink:href="10928_2023_9884_Article_Equb.gif"/></alternatives></disp-formula></p><p id="Par18">Model predicted PFS was compared to clinical trial data published for T-DM1 [<xref rid="CR39" ref-type="bibr">39</xref>] and T-DXd [<xref rid="CR40" ref-type="bibr">40</xref>, <xref rid="CR41" ref-type="bibr">41</xref>] as described above. Patients removed from the trial due to reasons other than tumor progression were censored by sampling patient drop out times from an exponential distribution, assuming that each individual dropped out of the trial independent of other individuals. Sampling rates, intervals, and number of patients sampled were selected to mimic those observed in the clinical trials.</p></sec><sec id="Sec13"><title>Data sources and model implementation</title><p id="Par19">All the data used in model parameterization was extracted from the literature. Datasets used for model calibration and validation were digitized from the literature using WebPlotDigitizer (<ext-link xlink:href="https://automeris.io/WebPlotDigitizer" ext-link-type="uri">https://automeris.io/WebPlotDigitizer</ext-link>, version 4.6, Pacifica, CA). Models were fit to data using gradient-descent methods and simulations were performed using QSP Notebook software (Applied Biomath LLC, Concord MA). As per Applied BioMath™ internal guidance, model QC was completed by an independent modeler, not involved in model development. Figures for the manuscript were generated using Biorender.com and plots using matplotlib [<xref rid="CR44" ref-type="bibr">44</xref>].</p></sec></sec><sec id="Sec14"><title>Results</title><sec id="Sec15"><title>Modeling the cellular disposition of T-DM1 and lys-smcc-DM1 in cancer cells</title><p id="Par20">A cellular model was developed to characterize the in vitro disposition of T-DM1 and its released payload in target tumor cells. Model simulations versus experimental observations for the model calibration and validation to trastuzumab uptake into cancer cells [<xref rid="CR14" ref-type="bibr">14</xref>, <xref rid="CR14" ref-type="bibr">14</xref>, <xref rid="CR15" ref-type="bibr">15</xref>] are shown in the supplementary material (Figure S1B). Simulated profiles of T-DM1 incubations in BT-474EEI, SK-BR-3 and MCF7-neo/HER2 breast cancer cells are compared with observed results from this experiment [<xref rid="CR16" ref-type="bibr">16</xref>] in Fig. <xref ref-type="fig" rid="Fig3">3</xref> and the model parameters are shown in Table S2a. The model predicted concentration versus time profiles of intracellular and extracellular species for all cell lines very well. Consistent with observed data, model simulations showed that concentrations of unconjugated DM1 were higher inside cells than in the media, which is likely due to T-DM1 binding to tubulin intracellularly and its slow diffusion out of cells.<fig id="Fig3"><label>Fig. 3</label><caption><p>Observed (symbols) and in vitro cellular model calibrated (lines) T-DM1 in vitro disposition data. BT-474EEI, SK-BR-3 and MCF-7-neo/HER2 cells were incubated with T-[<sup>3</sup>H]DM1 for 2 h on ice (gray shaded area), washed, and intracellular and extracellular concentrations of DM1 catabolites were measured [<xref rid="CR16" ref-type="bibr">16</xref>]. The in vitro cellular model was used to simulate these concentrations and compared to observed data</p></caption><graphic id="MO4" xlink:href="10928_2023_9884_Fig3_HTML"/></fig></p></sec><sec id="Sec16"><title>Modeling the mouse PK of T-DM1 and T-DXd and predicting tumor payload concentrations of T-DM1</title><p id="Par21">The ADC and total antibody plasma PK data for both T-DM1 and T-DXd in non-tumor bearing mice [<xref rid="CR16" ref-type="bibr">16</xref>, <xref rid="CR22" ref-type="bibr">22</xref>] were described well by the model (Figure S4). The model was able to fit the data for both ADCs simultaneously using the same distribution parameters and antibody half-life (11.6 days). The deconjugation rate was estimated separately for the two ADCs and was much slower for T-DXd compared to T-DM1, indicating a more stable linker in circulation. The half-life values for DM1 and DXd were estimated to be 3.5 h and 0.8 h, respectively, from PK data for a maytansine with structural similarity to DM1, and DXd in non-tumor bearing mice.</p><p id="Par22">The mouse plasma PK model for T-DM1 was combined with a mechanistic tumor uptake model and the cellular model to predict tumor concentrations of DM1 in xenograft mice. Figure <xref ref-type="fig" rid="Fig4">4</xref>A shows fitting of the model to observed data for DM1 concentration versus time profiles in plasma and tumor of BT-474EEI bearing xenograft mice, after IV administration of T-[H]<sup>3</sup>-DM1 (300 µg/kg DM1 based dose) [<xref rid="CR16" ref-type="bibr">16</xref>]. Mouse PK parameters are summarized in Table S2b.<fig id="Fig4"><label>Fig. 4</label><caption><p>In vivo mouse model calibration for T-DM1 and T-DXd. <bold>A</bold> Observed (symbols) and in vivo PK model calibrated (lines) DM1 disposition data. Following iv administration of T-[H]<sup>3</sup>-DM1 (300 µg/kg DM1 based dose) to BT-474EEI tumor bearing xenograft mice, levels of DM1 were measured in plasma and tumor [<xref rid="CR16" ref-type="bibr">16</xref>]. Observed (symbols) and in vivo model calibrated (lines) tumor growth inhibition following IV administration of <bold>A</bold> T-DM1 to BT474-EEI, <bold>B</bold> T-DM1 to N87 and <bold>C</bold> T-DXd to N87 mouse xenograft studies. Model simulations of predicted payload concentrations in plasma and tumor (total and unconjugated), and model predicted TGI versus observed data are shown for T-DM1 and T-DXd [<xref rid="CR16" ref-type="bibr">16</xref>, <xref rid="CR24" ref-type="bibr">24</xref>, <xref rid="CR26" ref-type="bibr">26</xref>–<xref rid="CR29" ref-type="bibr">29</xref>]</p></caption><graphic id="MO2" xlink:href="10928_2023_9884_Fig4_HTML"/></fig></p></sec><sec id="Sec17"><title>Modeling tumor growth inhibition in mouse following administration of T-DM1 and T-DXd</title><p id="Par23">The predicted intracellular tumor payload concentrations were linked to a model of tumor growth and tumor cell killing and used to fit TGI data from a range of xenograft mouse experiments. Figure <xref ref-type="fig" rid="Fig4">4</xref> shows the fitting of the model to TGI data from a BT474-EEI mouse xenograft study following dosing of T-DM1, and N87 mouse xenograft studies following dosing of T-DM1 and T-DXd. Model simulations of predicted payload concentrations in plasma and tumor, and model calibrated TGI versus observed data are shown for each dataset. Additional figures showing model fits to TGI data from BT-474 and KPL-4 mouse xenograft studies following different doses of T-DM1 are shown in Figure S5. In each case, the model was able to characterize the data well, estimating only parameters associated with tumor cell growth and tumor cell killing (see Table S2c). This demonstrates the flexibility of the model in capturing data from diverse xenograft data, with different tumor growth rates across cell lines. The model parameters (Table S2c) provided a set of PD parameters for preclinical to clinical translation of T-DM1 and T-DXd efficacy.</p></sec><sec id="Sec18"><title>Predicting PK of T-DM1 and T-DXd and their released payloads in the plasma of cancer patients</title><p id="Par24">The human PK model was able to predict the clinical plasma PK data for T-DM1 and T-DXd for all analytes (ADC, total antibody, and payload; Fig. <xref ref-type="fig" rid="Fig5">5</xref>) without estimation of any parameters. The model was able to capture the nonlinearity of the ADC concentrations in the dose escalation data due to target mediated drug disposition (Fig. <xref ref-type="fig" rid="Fig5">5</xref>C and <xref ref-type="fig" rid="Fig5">D</xref>). PK model parameters (Table S2d) were used in subsequent clinical TGI and PFS simulations.<fig id="Fig5"><label>Fig. 5</label><caption><p>Observed (symbols) and model predicted (lines) clinical PK of T-DM1 and T-DXd. Simulations of ADC, total antibody and released payload following IV administration of <bold>A</bold> T-DM1 to HER2+ MBC patients at 3.6 mg/kg, compared with clinical data from cycle 1 [<xref rid="CR31" ref-type="bibr">31</xref>], and <bold>B</bold> T-DXd to HER2+ patients with breast, gastric or gastro-oesophageal carcinomas at 6.4 mg/kg, compared to data from cycles 1–3 [<xref rid="CR32" ref-type="bibr">32</xref>]. Simulations of ADC concentrations in the same patient populations following IV administration of <bold>C</bold> T-DM1 from 0.3 to 4.8 mg/kg and <bold>D</bold> T-DXd from 0.8 to 8.0 mg/kg Q3W, with comparison to clinical data</p></caption><graphic id="MO5" xlink:href="10928_2023_9884_Fig5_HTML"/></fig></p></sec><sec id="Sec19"><title>Predicting tumor growth inhibition following administration of T-DM1 and T-DXd to HER2+ MBC patients</title><p id="Par25">Human model simulations of T-DM1 and T-DXd plasma PK, total tumor payload concentrations and TGI following IV administration at 0.3–6.4 mg/kg are shown in Fig. <xref ref-type="fig" rid="Fig6">6</xref>. The simulations predicted that T-DM1 and T-DXd have very similar ADC plasma PK, but T-DXd delivers higher concentrations of payload to the tumor, which contributes to greater predicted efficacy compared to T-DM1 at the same doses.<fig id="Fig6"><label>Fig. 6</label><caption><p>Model predictions of T-DM1 and T-DXd plasma PK, tumor payload concentrations and efficacy (tumor growth inhibition) using nominal patient parameters, following IV administration at 0.3–6.4 mg/kg. T-DM1 and T-DXd have very similar ADC plasma PK, but T-DXd delivers higher concentrations of payload to the tumor, one of the factors that could result in greater predicted efficacy compared to T-DM1 at the same doses</p></caption><graphic id="MO6" xlink:href="10928_2023_9884_Fig6_HTML"/></fig></p></sec><sec id="Sec20"><title>PRCC sensitivity analysis</title><p id="Par26">A global sensitivity analysis was performed using the T-DM1-parameterized human model to understand which parameters impacted tumor volume response. The most sensitive parameters (Figure S6) included exponential tumor doubling time (<inline-formula id="IEq39"><alternatives><tex-math id="M81">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$t_{double}$$\end{document}</tex-math><mml:math id="M82"><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi mathvariant="italic">double</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq39.gif"/></alternatives></inline-formula>) and a combination of pharmacodynamic parameters (tumor death transduction delay (τ), maximum kill rate (<inline-formula id="IEq40"><alternatives><tex-math id="M83">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k_{kill, max}$$\end{document}</tex-math><mml:math id="M84"><mml:msub><mml:mi>k</mml:mi><mml:mrow><mml:mi>k</mml:mi><mml:mi>i</mml:mi><mml:mi>l</mml:mi><mml:mi>l</mml:mi><mml:mo>,</mml:mo><mml:mi>m</mml:mi><mml:mi>a</mml:mi><mml:mi>x</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq40.gif"/></alternatives></inline-formula>), and payload concentration at half maximum kill rate (<inline-formula id="IEq41"><alternatives><tex-math id="M85">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$kc_{50}$$\end{document}</tex-math><mml:math id="M86"><mml:mrow><mml:mi>k</mml:mi><mml:msub><mml:mi>c</mml:mi><mml:mn>50</mml:mn></mml:msub></mml:mrow></mml:math><inline-graphic xlink:href="10928_2023_9884_Article_IEq41.gif"/></alternatives></inline-formula>). Other notable sensitive parameters included target related parameters such as soluble HER2 half-life and number of HER2+ cells in normal tissues. Drug parameters including DAR, ADC/Ab permeability and half-life were also sensitive.</p></sec><sec id="Sec21"><title>Virtual clinical trial simulations to predict progression free survival of T-DM1 and T-DXd in HER2+ MBC patients</title><p id="Par27">T-DM1 simulations were performed at 3.6 mg/kg IV Q3W to match the phase 2 clinical trial data [<xref rid="CR39" ref-type="bibr">39</xref>]. The simulations were used to calculate the probability of progression free survival out to 6–14 months for patients with high or low HER2 expression levels (Fig. <xref ref-type="fig" rid="Fig7">7</xref>A). For T-DXd, patients were simulated with 5.4 mg/kg IV Q3W dosing as in the phase 2 clinical trial data [<xref rid="CR40" ref-type="bibr">40</xref>] and phase 3 data [<xref rid="CR41" ref-type="bibr">41</xref>] (Fig. <xref ref-type="fig" rid="Fig7">7</xref>B). The model predictions of PFS were in good agreement with observed clinical trial data for T-DM1 and T-DXd. The model predicted greater PFS for T-DXd and could discriminate between patients with high and low HER2 expression for both ADCs, consistent with the data. Predictions were sensitive to tumor growth and death rates, and level of censorship in the studies.<fig id="Fig7"><label>Fig. 7</label><caption><p>Comparison of clinical trial simulations of progression free survival (PFS) with observed clinical trial data for T-DM1 and T-DXd. <bold>A</bold> T-DM1 PFS simulations (solid lines) and observed data (dotted lines) from a phase 2 clinical trial [<xref rid="CR39" ref-type="bibr">39</xref>] following administration of T-DM1 to HER2+ metastatic breast cancer (MBC) patients at 3.6 mg/kg Q3W for 14 months. <bold>B</bold> T-DXd PFS simulations (solid lines) and observed data (dotted lines) from a phase 2 [<xref rid="CR40" ref-type="bibr">40</xref>] and a phase 3 clinical trial [<xref rid="CR41" ref-type="bibr">41</xref>] following administration of T-DXd at 5.4 mg/kg Q3W to HER2+ MBC patients for 29 months. For both T-DM1 and T-DXd, clinical simulations and data were stratified based upon HER2 high (1e6/cell) and HER2 low (2e4) expression levels in MBC patients</p></caption><graphic id="MO7" xlink:href="10928_2023_9884_Fig7_HTML"/></fig></p></sec></sec><sec id="Sec22"><title>Discussion</title><sec id="Sec23"><title>Encouraging clinical data, but also many clinical failures. How do we realize the clinical success of ADCs?</title><p id="Par28">ADCs are among the fastest growing drug classes in oncology. They have demonstrated impressive clinical efficacy against treatment refractory cancers, with approvals in numerous and diverse indications [<xref rid="CR45" ref-type="bibr">45</xref>]. However, many ADCs continue to fail in the clinic due to either lack of efficacy/ improvement over standard of care, or failure to reach efficacious doses due to dose limiting toxicities. Better methods are required to predict ADC efficacy and toxicity from preclinical data to optimize successful design and development of ADCs. There is also a need to learn from the previous generation of ADCs, in particular high level clinical failures, to understand the barriers that need to be overcome to enable clinical success and broader clinical utilization of ADCs.</p><p id="Par29">The efficacy of an ADC depends on the intricacies of how the antibody, linker, and payload components interact with the tumor, which has important clinical implications. This requires a more quantitative understanding of ADC processing and activity that occurs prior to and following antibody-receptor engagement both on a cell-specific and a tumor-specific basis. Such knowledge would enable tuning of ADC antibody, linker and payload properties specifically for the selected target and tumor properties, informing better ADC design and selection. This would also facilitate development of a unifying framework enabling translation from preclinical studies to the clinic and prediction of clinical dose and regimen. Selection of doses for pivotal phase 2 clinical trials for ADCs by dose escalation to the maximum tolerated dose in the clinic may be a risky strategy that puts patients at risk for toxicities. A better approach to predict clinical dose and regimen for different indications is required, which would enable allocation of ADCs to those patients who are most likely to benefit from them. This is a two-part problem- there needs to be a better understanding of ADC targeting to the tumor and resulting efficacy, along with prevention of ADC uptake and payload release in normal tissues which can drive toxicity. In this manuscript we sought to establish a quantitative platform for prediction of ADC efficacy in the clinic via translation from preclinical data to the clinic. A second piece of work is ongoing to establish models to predict ADC toxicity in the clinic, which will be required for prediction of therapeutic index.</p></sec><sec id="Sec24"><title>Why is QSP modeling a useful tool for this problem? Why do we need a platform QSP model for ADCs?</title><p id="Par30">Mathematical modeling approaches can be a useful tool for understanding mechanisms of drug action, increasing productivity, and enhancing decision making [<xref rid="CR46" ref-type="bibr">46</xref>]. Empirical PK/PD models have proven very useful in the preclinical and clinical development of ADCs to maximize information obtained from experimental data, while minimizing resource utilization [<xref rid="CR8" ref-type="bibr">8</xref>, <xref rid="CR24" ref-type="bibr">24</xref>, <xref rid="CR47" ref-type="bibr">47</xref>]. However, they are limited in their ability to predict efficacy across different targets and to inform design parameters. To understand the complexity of ADCs more mechanistic approaches are required, such as quantitative systems pharmacology (QSP) models that describe the dynamic interaction between biological systems and drugs [<xref rid="CR48" ref-type="bibr">48</xref>]. A key feature of these models is their explicit distinction between ‘drug’ and ‘system’ parameters. For ADCs, system parameters include receptor expression on tumor and normal cells, internalization or turnover rates, and tumor growth rates, which can be obtained from the literature or prior experiments. Drug-specific parameters include PK parameters such as half-life, and pharmacological parameters such as target affinity and intrinsic efficacy of ADCs. As such QSP models contain sufficient mechanistic details to enable understanding of the processes critical to an ADC’s performance and to perform multiscale predictions.</p><p id="Par31">Several QSP models have been developed for ADCs and are summarized by Lam et al<italic>.</italic> [<xref rid="CR7" ref-type="bibr">7</xref>]. These models describe cellular mechanisms, tumor penetration, preclinical to clinical translation and clinical simulations. For example, a seminal paper by Shah and coworkers presented a bench to bedside translation of brentuximab vedotin using a multiscale mechanistic PK/PD model [<xref rid="CR49" ref-type="bibr">49</xref>]. This model characterized the ADC and payload PK at the cellular level and in vivo in mouse plasma. It introduced a novel tumor disposition model to predict tumor payload concentrations of ADC and payload. The model also contained a tumor growth inhibition model which was used to characterize mouse xenograft data, and then translated to human. The model successfully predicted PFS rates in the clinic for brentuximab vedotin. A similar model structure and translational strategy was also applied by others for successful prediction of clinical outcome for T-DM1 [<xref rid="CR34" ref-type="bibr">34</xref>, <xref rid="CR50" ref-type="bibr">50</xref>] and inotuzumab ozogamicin [<xref rid="CR51" ref-type="bibr">51</xref>]. The latter paper was used to recommend a modified, fractionated regimen for a new indication of inotuzumab (ALL) which was successfully employed in the clinic. Vasalou and coworkers developed a mechanistic model of the solid tumor in a mouse model, incorporating essential mechanisms involved in ADC tumor localization and distribution [<xref rid="CR6" ref-type="bibr">6</xref>]. They focused on intrinsic properties of the tumor, and controllable design parameters. This was a pivotal paper showing the impact of ADC design on tumor penetration, and quantitative solutions to the binding site barrier effect. This analysis was further explored by others [<xref rid="CR52" ref-type="bibr">52</xref>, <xref rid="CR53" ref-type="bibr">53</xref>], including Khera et al<italic>.</italic> [<xref rid="CR54" ref-type="bibr">54</xref>] who developed a model to study payload distribution as a function of antibody dose, payload dose, and payload properties. This work highlighted the importance of increasing ADC tissue penetration irrespective of bystander effects. Interestingly there are fewer publications on QSP models for ADC toxicity [<xref rid="CR55" ref-type="bibr">55</xref>, <xref rid="CR56" ref-type="bibr">56</xref>], which is a definite gap in the science.</p><p id="Par32">Although much progress has been made, there was a need for a more holistic platform model suitable for ADCs with varying mechanisms of action, that could be used for preclinical to clinical translation for ADCs with different tumor targets and classes of linkers and payloads. Platform models are QSP models which provide a common integrated quantitative knowledge repository for continued preclinical and clinical evaluation [<xref rid="CR57" ref-type="bibr">57</xref>]. They are not specific to a particular drug and therefore can be re-applied, providing a mechanistic framework for predicting efficacy distinct from other pharmacometrics strategies.</p></sec><sec id="Sec25"><title>How did we approach the development of a platform QSP model for ADC efficacy?</title><p id="Par33">This manuscript describes our initial steps towards a platform model for ADCs, integrating multiple data types and established knowledge to predict ADC efficacy (Fig. <xref ref-type="fig" rid="Fig1">1</xref>). Platform model development focused initially on the use of T-DM1 and T-DXd as test ADCs. These ADCs were selected as (1) there was sufficient data in the public domain for model calibration/ validation and (2) despite binding to the same target (HER2), they have disparate intracellular mechanisms of action which enable testing of different components of the model. Model development was approached in a modular and multi-scale fashion (Fig. <xref ref-type="fig" rid="Fig2">2</xref>), with verification of model predictions applied at each stage of the process by comparison to representative datasets. Although the model was trained on data for HER-2 ADCs, the framework is relevant for other ADCs simply by substitution of relevant parameters.</p></sec><sec id="Sec26"><title>Describing the intracellular mechanism of action of ADCs</title><p id="Par34">First, a cellular model was developed to characterize ADC disposition and release of payload in the target tumor cells (Figure S1A). This model was used to describe in vitro incubations and was the basis of the tumor compartment in the in vivo models. The goal was to describe greater detail at the intracellular level compared to previous models, in order to sufficiently describe ADCs with different mechanisms of payload release and disposition. The model includes extracellular ADC deconjugation and competition of ADC with antibody for binding to the target cell, which is not accounted for in previous models. Upon binding to HER2, the ADC is internalized, and both the free and bound receptor can be recycled back to the cell surface. This was especially important for HER2-ADCs as HER2 exhibits a high recycling fraction [<xref rid="CR58" ref-type="bibr">58</xref>], and recycling may occur before payload can be released inside the cell. Following internalization into cancer cells, T-DM1 traffics from the endosome to the lysosome where it undergoes proteolytic degradation, which releases the cytotoxic DM1-linker-lysine-metabolite (lysine-MCC-DM1) [<xref rid="CR59" ref-type="bibr">59</xref>, <xref rid="CR60" ref-type="bibr">60</xref>]. In contrast, upon internalization of T-DXd, cleavage of the protease linker results in release of the DXd payload in the endosomes [<xref rid="CR61" ref-type="bibr">61</xref>]. In the model, these different mechanisms are described by a degradation rate constant for T-DM1 describing release of payload along with the degradation of the antibody. For T-DXd an additional rate is included to describe linker-cleavage mediated release of the payload in the endosomes. Released payload can be transported to the cytosol where it is free to bind to its intracellular target (tubulin for lys-MCC-DM1 and TOPO-1 for DXd). The released payload species can also leave and re-enter the tumor cell, which is a function of its physicochemical properties and affinity for transport proteins.</p><p id="Par35">The parameters for the cellular model were extracted from the literature or determined by fitting to data (Table S2). These included system properties such as HER2 expression across tumor cell lines, rates of internalization and recycling, and levels of tubulin or TOPO-1 inside cells. It also included drug specific properties including deconjugation rates and DAR for T-DM1 and T-DXd, trastuzumab binding affinity to HER2, DM1 affinity to tubulin and DXd inhibition of TOPO-1, and cellular efflux/influx rates of payload. Intracellular and extracellular payload diffusion rates were taken from Khera et al<italic>.</italic> [<xref rid="CR54" ref-type="bibr">54</xref>] who performed a computational analysis to estimate these values based on parallel artificial membrane assay (PAMPA) data and physicochemical properties. The intracellular payload parameters can be the most difficult to obtain for ADC models, but since intracellular payload concentrations of payload drive efficacy (and toxicity) they are of fundamental importance [<xref rid="CR62" ref-type="bibr">62</xref>, <xref rid="CR63" ref-type="bibr">63</xref>]. More reports of these studies are appearing in the literature, but further experiments are needed [<xref rid="CR54" ref-type="bibr">54</xref>, <xref rid="CR64" ref-type="bibr">64</xref>]. The model was validated by comparison of model simulations to experimental results from diverse publications [<xref rid="CR14" ref-type="bibr">14</xref>–<xref rid="CR16" ref-type="bibr">16</xref>], following administration of trastuzumab and T-DM1 (Fig. <xref ref-type="fig" rid="Fig3">3</xref> and S1B). In vitro cellular data was not available for T-DXd; however the in vitro model has been used to simulate T-DXd disposition and to make comparisons with T-DM1 [<xref rid="CR65" ref-type="bibr">65</xref>].</p></sec><sec id="Sec27"><title>Implementation of a mechanistic tumor uptake model to predict intracellular payload concentrations</title><p id="Par36">For accurate prediction of extent and time course of uptake of drug species into solid tumors, a mechanistic tumor distribution model was included. The model describes the exchange of ADC, Ab, and payload between the systemic circulation and tumor extracellular space using permeability and diffusion terms determined by their respective molecular size. The size of the tumor determines the extent of the distribution via either surface area or vascular exchange, representing avascular micro metastases and larger vascularized tumors, respectively. Previous publications [<xref rid="CR34" ref-type="bibr">34</xref>, <xref rid="CR49" ref-type="bibr">49</xref>–<xref rid="CR51" ref-type="bibr">51</xref>] have shown this approach can predict tumor ADC and payload concentration in mouse studies [<xref rid="CR66" ref-type="bibr">66</xref>] and is clinically translatable. An important feature of the model is the dynamic interaction between the tumor disposition parameters and tumor size, where changes in tumor volume are directly able to influence the concentration of payload in the tumor, which is in turn responsible for the size of the tumor. To predict tumor payload concentrations in xenograft-bearing mice, the plasma PK model was linked to the tumor uptake model. Once in the tumor extracellular environment, cellular disposition of ADC, Ab and payload was then characterized using the parameters defined from the in vitro cellular model. For T-DM1 we show that the model is capable of predicting total tumor DM1 concentrations and intracellular concentrations of unconjugated DM1 in mice (Fig. <xref ref-type="fig" rid="Fig4">4</xref>). To accurately predict tumor DM1 concentration, it was necessary to use an intracellular tubulin concentration of 65 nM, which was the concentration estimated from previous modeling analyses [<xref rid="CR34" ref-type="bibr">34</xref>, <xref rid="CR49" ref-type="bibr">49</xref>, <xref rid="CR50" ref-type="bibr">50</xref>]. This value is lower than experimental estimates of tubulin concentration from literature (range of 10–20 µM [<xref rid="CR67" ref-type="bibr">67</xref>]) which were too high to describe the observed data. The current implementation of the model does not incorporate spatial distribution across the tumor tissue, but since it is based upon a general Krogh cylinder structure, it could be expanded to include this, which may be particularly useful for optimizing the design of ADCs.</p></sec><sec id="Sec28"><title>Tumor growth inhibition model</title><p id="Par37">To describe the reduction in tumor volume in the mouse following administration of T-DM1 and T-DXd to xenograft models, an established TGI model was applied [<xref rid="CR24" ref-type="bibr">24</xref>]. This model was selected as it includes a comprehensive tumor growth model describing exponential, linear and logistic growth, combined with a transit compartment tumor cell killing model, capable of describing different tumor growth and killing characteristics observed in vitro, in vivo, and in the clinic. Tumor killing was described as a function of intracellular unconjugated tumor payload concentration, predicted by the physiological PK model described above. As such the model does not specifically delineate between direct (targeted) tumor killing and bystander killing. Bystander killing refers to the ability of payloads to diffuse through membranes out of directly targeted cells into neighboring non-targeted cells resulting in cell death [<xref rid="CR68" ref-type="bibr">68</xref>]. This mechanism of action has been postulated to be important for the treatment of tumors with heterogeneous expression of ADC targets, and thus may be an important design consideration. Bystander effect is observed for ADCs with cleavable linkers, like T-DXd, where smaller and more lipophilic payloads are released (e.g., DXd) that can diffuse across membranes [<xref rid="CR69" ref-type="bibr">69</xref>]. ADCs with non-cleavable linkers, such as T-DM1, release larger/more hydrophilic amino acid-linker-payload metabolites (e.g., lysine-MCC-DM1) which do not readily diffuse across membranes to kill bystander cells. The impact of bystander killing on efficacy is not fully understood and has been found to be less efficient than direct killing [<xref rid="CR54" ref-type="bibr">54</xref>]. Nonetheless, an addition to the model currently under testing includes an untargeted cell population, capable of exploring bystander effect.</p><p id="Par38">The current model was able to describe the tumor growth and tumor cell killing observed following administration of different doses and regimens of T-DM1 and T-DXd to a range of mouse xenograft models (Fig. <xref ref-type="fig" rid="Fig4">4</xref>). The simulations predicted higher concentrations of tumor payload for T-DXd compared to T-DM1 for a given dose in the same tumor model, which was associated with greater efficacy of T-DXd. This may be due to the higher DAR of T-DXd compared to T-DM1, or more efficient release of its payload inside the cell resulting from its cleavable linker.</p></sec><sec id="Sec29"><title>Expanded human PK model accounting for multiple sinks</title><p id="Par39">To describe the human PK, a comprehensive model was developed for a mechanistic description of ADC, Ab, and payload disposition in the systemic circulation and normal tissues including binding to various receptor sinks. Many ADCs bind to targets which have some level of expression on healthy cells in addition to tumor cells, and some of these receptors can also be shed from the membrane. For example, HER2 has been shown to be expressed on circulating NK cells, cardiomyocytes, skeletal muscle cells, and colonic epithelial cells [<xref rid="CR35" ref-type="bibr">35</xref>]. The extracellular domain of HER2 is also shed from the cell surface, and serum concentrations of HER2 have been shown to be higher in metastatic breast cancer patients compared to healthy females [<xref rid="CR70" ref-type="bibr">70</xref>]. Both the healthy cells expressing tumor target and the soluble target can act as a sink for ADC (or released antibody), reducing the exposure to the tumor cells for a given ADC dose. For HER2 this was demonstrated for trastuzumab, where high levels of serum extracellular domain are associated with rapid clearance and decreased benefit from trastuzumab therapy [<xref rid="CR70" ref-type="bibr">70</xref>–<xref rid="CR72" ref-type="bibr">72</xref>]. In addition, binding to these cells can result in on-target/ off-tumor toxicities [<xref rid="CR73" ref-type="bibr">73</xref>] which can limit the dosing of ADCs in the clinic. As such, this is an important feature of a platform model. The model also describes deconjugation of ADC to release payload, and catabolism of ADC which can also release payload. Another distinguishing feature of the model is that it contains physiological volumes for central and peripheral compartments for ADC, Ab and payload, and as such moves away from more empirical PK descriptions. The model was able to predict the PK of T-DM1 and T-DXd in the clinic, including the non-linear PK of ADCs observed across the dose range explored in Phase 1a studies, without estimation of any parameters (Fig. <xref ref-type="fig" rid="Fig5">5</xref>). It was also capable of describing total antibody and payload concentrations observed at the efficacious doses of T-DM1 and T-DXd (Fig. <xref ref-type="fig" rid="Fig5">5</xref>).</p></sec><sec id="Sec30"><title>Predictions of tumor volume reduction and PFS in HER2+ MBC patients</title><p id="Par40">The next step in the process was to combine the human PK model with the TGI model, to predict T-DM1 and T-DXd efficacy in HER2+ MBC patients. The overall structure of the mouse TGI model was kept the same, but the mouse system parameters such as initial tumor size, tumor growth rate, and receptor expression were replaced with clinically relevant parameters for HER2+ MBC. The drug effect parameters, including the maximum kill rate and the potency values were kept the same as those estimated from mouse TGI studies for T-DM1 and T-DXd. Initially the model was used to perform simulations for a nominal patient receiving T-DM1 and T-DXd given the same doses and regimen. The parameters relating to the different payloads of the two HER2 ADCs were different including DAR, deconjugation rate, cleavage rate in the endo/lysosomal compartment and intracellular target binding. In addition, the drug effect parameters including kill rate and potency were different. However, all other parameters in the model were the same. “The simulations (Fig. <xref ref-type="fig" rid="Fig6">6</xref>) show that for the same dose, tumor payload concentrations are much higher for T-DXd administration compared to T-DM1, which could be a factor that contributes to higher predicted efficacy for T-DXd, consistent with clinical observations. This points to intracellular tumor payload concentration, a function of both target mediated and target-independent uptake along with tumor penetration and intrinsic killing activity of the payload as factors that could be used to optimize drug design.</p><p id="Par41">To identify the most sensitive parameters impacting tumor volume predictions, and to guide the selection of parameters for virtual clinical trial simulations, a global sensitivity analysis was completed (Figure S6). Parameters controlling tumor growth and death were the most sensitive parameters in the model. In particular, tumor growth rate has been shown to be a sensitive parameter from previous model analyses for ADCs [<xref rid="CR51" ref-type="bibr">51</xref>]. As such, exponential and linear growth rates were varied in the clinical simulations, along with the pharmacodynamic killing parameters (<italic>k</italic><sub><italic>kill</italic></sub> and <italic>kc</italic><sub><italic>50</italic></sub>). The model was used to perform virtual clinical trial simulations for T-DM1 and T-DXd, emulating conditions in pivotal phase 2 and phase 3 trials [<xref rid="CR39" ref-type="bibr">39</xref>–<xref rid="CR41" ref-type="bibr">41</xref>]. The model was able to predict PFS for T-DM1 and T-DXd that agreed with observed clinical data (Fig. <xref ref-type="fig" rid="Fig7">7</xref>), providing validation of model prediction of clinical outcome. In addition, the model predicted a difference in PFS for patients with low HER2 expression (20,000 receptors/cell, approximately HER2 1+) compared to patients with high HER2 expression (1 million receptors/cell, approximately HER2 3+). This was especially noticeable for T-DM1, which was predicted to have substantially shorter PFS in patients with low HER2 expression status compared to patients with high HER2 expression, as observed in clinical trial data [<xref rid="CR39" ref-type="bibr">39</xref>–<xref rid="CR41" ref-type="bibr">41</xref>]. In contrast, T-DXd was predicted to have longer PFS in patients with both high and low HER2 expression status. This suggests that the model could be used for head-to-head in silico predictions of clinical efficacy for different ADCs, which could be useful for early-stage assessments of efficacy differentiation and to provide rationale for pursuing clinical development. The model could also be used to investigate different clinical diagnostics of efficacy such as receptor expression, supporting precision medicine strategies for ADCs in the clinic.</p></sec><sec id="Sec31"><title>Model application and future developments</title><p id="Par42">Significant progress has been made in the development of this next generation multiscale QSP model for ADCs, capable of supporting project decisions from exploratory research through to late-stage clinical trials. This platform model can be applied to other ADCs with different payloads and targeting different receptors, using data routinely collected for ADCs in the discovery process, or available from the literature (Table <xref ref-type="table" rid="Tab1">1</xref> and Table S4). For example, key target data to collect would include receptor expression, internalization rates and affinities. For specific payloads, important data would include affinity to intracellular target (or potencies) and permeability data, such as PAMPA. To investigate optimal ADC design at early stages, the model could be used to predict and optimize intracellular payload concentration as a surrogate for ADC efficacy, with comparison to competitor ADCs with similar payload class and clinical results. The model sensitivity to the concentration of payload in the tumor at half maximum kill rate suggests that maximizing the concentration of payload delivered to the tumor will enable successful treatment of more patients (assuming lack of toxicity). It is important to note that because this model does not explicitly capture tissue penetration, it is most useful in comparing molecules with similar biophysical properties. Likewise, the model captures killing on target-positive cells; thus, any bystander effect of target-negative cells are not considered, and the model might underestimate therapeutic efficacy. Once a lead ADC has been selected, the model can be linked to tumor cell killing, by fitting data from mouse TGI experiments, and then translated to the clinic. Strategies for answering specific drug development questions can be quite modular as the modeling can include or exclude various data complementary to the question poised. The goal is that the ADC model can provide a translational framework integrating data to provide a clearer understanding of the system, to help with design and to determine overall risk, so that ADCs could be progressed with a higher probability of success.<table-wrap id="Tab1"><label>Table 1</label><caption><p>Use-cases for the ADC platform model</p></caption><table rules="groups" frame="hsides"><thead><tr><th align="left">Question 1</th><th colspan="3" align="left">How do I optimize ADC design at early stages?</th></tr></thead><tbody><tr><td align="left">Models to use</td><td colspan="3" align="left">Cellular PK + Human PK</td></tr><tr><td align="left">Output</td><td colspan="3" align="left">Predicted intracellular tumor payload concentration and/or binding to intracellular target for antibody target positive cells</td></tr><tr><td rowspan="1" align="left">Data required</td><td align="left">Parameters to measure experimentally or find in literature</td><td align="left">Optional Parameters to measure or find in literature<sup>a</sup></td><td align="left">Parameters to assume and scan over with simulations</td></tr><tr><td align="left"/><td align="left"><p>• Receptor expression (tumor and healthy cells)</p><p>• Internalization and degradation rates</p><p>• Soluble target expression and turnover rates</p><p>• Antibody binding affinity for membrane target (e.g., SPR)</p><p>• Payload membrane permeability e.g., PAMPA</p></td><td align="left"><p>• Plasma stability</p><p>• Lysosomal stability</p><p>• Payload binding affinity for intracellular target</p><p>• Intracellular target concentration</p></td><td align="left"><p>• Assume typical ADC first order t<sub>1/2</sub> (e.g., 10 days)</p><p>• Assign an ADC affinity to membrane target (e.g., 1 nM)</p><p>• Assign a DAR (e.g., 4)</p><p>• Assume tissue penetration is equivalent across formats</p></td></tr><tr><td align="left">Example strategies</td><td colspan="3" align="left"><p>1. Calibrate to in vitro cellular disposition data of molecules with different DAR and affinities</p><p>2. Simulate tumor payload concentrations at different DARs, affinities, PK t<sub>1/2</sub> s etc</p><p>3. Optimize tumor payload concentrations with comparison to competitor ADCs with similar payload class and clinical results</p><p>4. Optimize binding to intracellular target, consistent with MOA of payload</p></td></tr></tbody></table><table rules="groups" frame="hsides"><thead><tr><th align="left">Question 2</th><th colspan="2" align="left">How do I translate from preclinical to clinical to predict<break/>clinical efficacious dose?</th></tr></thead><tbody><tr><td align="left">Models to use</td><td colspan="2" align="left">Cellular PK, Mouse PK + TGI, Human PK + TGI</td></tr><tr><td align="left">Output</td><td colspan="2" align="left">Prediction of clinical PK (including TMDD) and clinical tumor volume reduction</td></tr><tr><td rowspan="2" align="left">Data required</td><td colspan="2" align="left">All data from Question 1</td></tr><tr><td align="left"><p>Preclinical data:</p><p>• ADC/antibody/payload disposition data (if available)</p><p>• Mouse PK</p><p>• Mouse TGI vs different cell lines/PDX with different properties</p></td><td align="left"><p>Clinical data:</p><p>• ADC/antibody/payload disposition data (if available)</p><p>• Tumor doubling times</p></td></tr><tr><td align="left">Example strategy</td><td colspan="2" align="left"><p>1. Develop tumor cellular model and calibrate to in vitro cellular disposition data (if available)</p><p>2. Develop mouse PK model and mouse TGI model</p><p>3. Develop human PK model</p><p>4. Link tumor cellular model, human PK model and TGI model. Parameterize using PD parameters determined from mouse and human PK and systems parameters</p><p>5. Perform model simulations of tumor volume reductions at different doses and regimens</p><p>6. Complete a global sensitivity analysis to determine key parameters impacting tumor volume reduction</p><p>7. Perform virtual clinical trial simulations varying sensitive parameters across plausible ranges</p></td></tr></tbody></table><table-wrap-foot><p>Models and data required to address two standard questions asked during ADC drug development. Potential strategies are included to provide further context for motivation of the modeling questions</p><p><sup>a</sup>Plasma stability can be used to inform deconjugation rates, lysosomal stability can inform cleavage/degradation rates of ADC to release payload, and permeability can inform the rates of influx/efflux across cells. However, for common payloads parameter estimates are also available from this manuscript and from others (e.g., Khera et al. [<xref rid="CR54" ref-type="bibr">54</xref>])</p></table-wrap-foot></table-wrap></p><p id="Par43">Work is currently underway to validate predictions for other ADCs with varying mechanisms of action, including those that have failed in the clinic, which will frame the next version of the efficacy model. The modular nature of the model makes it suitable for expansion depending on the specific questions asked. For example, recent evidence indicates that heterogeneous tumoral distribution of ADCs can play a large role in limiting their efficacy [<xref rid="CR54" ref-type="bibr">54</xref>]. Therefore, to be most relevant for design-based questions, a spatial version of the Krogh cylinder model should be incorporated to ensure optimal tumor penetration of ADCs. Additionally, inclusion of a sub model to predict bystander mediated killing distinct from targeted cell killing would be beneficial. Other future enhancements of the model would be to include other mechanisms of killing associated with ADCs including antibody dependent cellular phagocytosis (ADCP) and antibody dependent cell cytotoxicity (ADCC) [<xref rid="CR74" ref-type="bibr">74</xref>]. Additional data would be needed to support inclusion of these processes in the platform model, but this would allow the model to answer questions with more granularity. Finally, and perhaps most importantly, prediction of ADC efficacy is only one part of the therapeutic index equation, and linkage to a toxicity model to predict dose limiting toxicities resulting from ADC administration will reduce ADC attrition in the clinic.</p></sec></sec><sec id="Sec32"><title>Conclusions</title><p id="Par44">In conclusion, the presented model is a step toward a platform QSP model and strategy for ADCs, integrating multiple types of data and knowledge to predict ADC efficacy. Compared to previous work, the model incorporates greater mechanistic detail, particularly at the intracellular level, to account for different mechanisms of ADC processing and payload release<italic>.</italic> It accounts for the disposition of the ADC, antibody, and payload inside and outside of the tumor, including binding to off-tumor on-target sinks. Alongside the model, the methods for translating to the clinic and performing virtual clinical trial simulations have been reassessed. The model was able to successfully predict clinical outcome for T-DM1 and T-DXd, and is a promising quantitative tool to support design, selection, and optimization of clinical dosing strategies for ADCs.</p></sec><sec sec-type="supplementary-material" id="Sec33"><title>Supplementary Information</title><p>Below is the link to the electronic supplementary material.<supplementary-material id="MOESM1" content-type="local-data"><media xlink:href="10928_2023_9884_MOESM1_ESM.docx"><caption><p>Supplementary file1 (DOCX 103 kb)</p></caption></media></supplementary-material><supplementary-material id="MOESM2" content-type="local-data"><media xlink:href="10928_2023_9884_MOESM2_ESM.docx"><caption><p>Supplementary file2 (DOCX 1025 kb)</p></caption></media></supplementary-material></p></sec></body><back><fn-group><fn><p><bold>Publisher's Note</bold></p><p>Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></fn></fn-group><ack><title>Acknowledgements</title><p>This work would not have been possible without the input from the following people: Andrew A. Marquis for helpful discussion and help with response to reviews, Jamie Nosbisch for independent model QC, Marissa Renardy for help with the PRCC global sensitivity analysis, Katharina Wilkins and Diana Marcantonio for work on Phase 1 of the grant, and John Burke for scientific advice and support of the grant work.</p></ack><notes notes-type="author-contribution"><title>Author contributions</title><p>BS, KRG, KM, MN, SP and AB completed the modeling and biology work for this manuscript. KRG, KM and MN provided manuscript figures and supplemental information. JA and KS provided advice and guidance, and reviewed the manuscript. AB wrote the manuscript.</p></notes><notes notes-type="funding-information"><title>Funding</title><p>This work is funded by an NIH/SBIR Grant 1R44GM134790-01A1.</p></notes><notes><title>Declarations</title><notes notes-type="COI-statement" id="FPar1"><title>Competing interests</title><p id="Par45">The authors declare no competing interests.</p></notes></notes><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR1"><person-group person-group-type="author"><name><surname>Fu</surname><given-names>Z</given-names></name><name><surname>Li</surname><given-names>S</given-names></name><name><surname>Han</surname><given-names>S</given-names></name><etal/></person-group><article-title>Antibody drug conjugate: the “biological missile” for targeted cancer therapy</article-title><source>Signal Transduct Target Ther</source><year>2022</year><volume>7</volume><fpage>93</fpage><?supplied-pmid 35318309?><pub-id pub-id-type="pmid">35318309</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR1">Fu Z, Li S, Han S et al (2022) Antibody drug conjugate: the “biological missile” for targeted cancer therapy. Signal Transduct Target Ther 7:93<pub-id pub-id-type="pmid">35318309</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR2"><label>2.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR2"><person-group person-group-type="author"><name><surname>Su</surname><given-names>Z</given-names></name><name><surname>Xiao</surname><given-names>D</given-names></name><name><surname>Xie</surname><given-names>F</given-names></name><etal/></person-group><article-title>Antibody-drug conjugates: recent advances in linker chemistry</article-title><source>Acta Pharm Sin B</source><year>2021</year><volume>11</volume><fpage>3889</fpage><lpage>3907</lpage><?supplied-pmid 35024314?><pub-id pub-id-type="pmid">35024314</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR2">Su Z, Xiao D, Xie F et al (2021) Antibody-drug conjugates: recent advances in linker chemistry. Acta Pharm Sin B 11:3889–3907<pub-id pub-id-type="pmid">35024314</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR3"><label>3.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR3"><person-group person-group-type="author"><name><surname>Panowski</surname><given-names>S</given-names></name><name><surname>Bhakta</surname><given-names>S</given-names></name><name><surname>Raab</surname><given-names>H</given-names></name><etal/></person-group><article-title>Site-specific antibody drug conjugates for cancer therapy</article-title><source>MAbs</source><year>2014</year><volume>6</volume><fpage>34</fpage><lpage>45</lpage><?supplied-pmid 24423619?><pub-id pub-id-type="pmid">24423619</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR3">Panowski S, Bhakta S, Raab H et al (2014) Site-specific antibody drug conjugates for cancer therapy. MAbs 6:34–45<pub-id pub-id-type="pmid">24423619</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR4"><label>4.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR4"><person-group person-group-type="author"><name><surname>Gerber</surname><given-names>H-P</given-names></name><name><surname>Sapra</surname><given-names>P</given-names></name><name><surname>Loganzo</surname><given-names>F</given-names></name><name><surname>May</surname><given-names>C</given-names></name></person-group><article-title>Combining antibody-drug conjugates and immune-mediated cancer therapy: what to expect?</article-title><source>Biochem Pharmacol</source><year>2016</year><volume>102</volume><fpage>1</fpage><lpage>6</lpage><?supplied-pmid 26686577?><pub-id pub-id-type="pmid">26686577</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR4">Gerber H-P, Sapra P, Loganzo F, May C (2016) Combining antibody-drug conjugates and immune-mediated cancer therapy: what to expect? Biochem Pharmacol 102:1–6<pub-id pub-id-type="pmid">26686577</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR5"><label>5.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR5"><person-group person-group-type="author"><name><surname>Mahalingaiah</surname><given-names>PK</given-names></name><name><surname>Ciurlionis</surname><given-names>R</given-names></name><name><surname>Durbin</surname><given-names>KR</given-names></name><etal/></person-group><article-title>Potential mechanisms of target-independent uptake and toxicity of antibody-drug conjugates</article-title><source>Pharmacol Ther</source><year>2019</year><volume>200</volume><fpage>110</fpage><lpage>125</lpage><?supplied-pmid 31028836?><pub-id pub-id-type="pmid">31028836</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR5">Mahalingaiah PK, Ciurlionis R, Durbin KR et al (2019) Potential mechanisms of target-independent uptake and toxicity of antibody-drug conjugates. Pharmacol Ther 200:110–125<pub-id pub-id-type="pmid">31028836</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR6"><label>6.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR6"><person-group person-group-type="author"><name><surname>Vasalou</surname><given-names>C</given-names></name><name><surname>Helmlinger</surname><given-names>G</given-names></name><name><surname>Gomes</surname><given-names>B</given-names></name></person-group><article-title>A mechanistic tumor penetration model to guide antibody drug conjugate design</article-title><source>PLoS ONE</source><year>2015</year><volume>10</volume><fpage>e0118977</fpage><?supplied-pmid 25786126?><pub-id pub-id-type="pmid">25786126</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR6">Vasalou C, Helmlinger G, Gomes B (2015) A mechanistic tumor penetration model to guide antibody drug conjugate design. PLoS ONE 10:e0118977<pub-id pub-id-type="pmid">25786126</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR7"><label>7.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR7"><person-group person-group-type="author"><name><surname>Lam</surname><given-names>I</given-names></name><name><surname>Pilla Reddy</surname><given-names>V</given-names></name><name><surname>Ball</surname><given-names>K</given-names></name><etal/></person-group><article-title>Development of and insights from systems pharmacology models of antibody-drug conjugates</article-title><source>CPT Pharmacometrics Syst Pharmacol</source><year>2022</year><volume>11</volume><fpage>967</fpage><lpage>990</lpage><?supplied-pmid 35712824?><pub-id pub-id-type="pmid">35712824</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR7">Lam I, Pilla Reddy V, Ball K et al (2022) Development of and insights from systems pharmacology models of antibody-drug conjugates. CPT Pharmacometrics Syst Pharmacol 11:967–990<pub-id pub-id-type="pmid">35712824</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR8"><label>8.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR8"><person-group person-group-type="author"><name><surname>Shah</surname><given-names>DK</given-names></name><name><surname>Loganzo</surname><given-names>F</given-names></name><name><surname>Haddish-Berhane</surname><given-names>N</given-names></name><etal/></person-group><article-title>Establishing in vitro-in vivo correlation for antibody drug conjugate efficacy: a PK/PD modeling approach</article-title><source>J Pharmacokinet Pharmacodyn</source><year>2018</year><volume>45</volume><fpage>339</fpage><lpage>349</lpage><?supplied-pmid 29423862?><pub-id pub-id-type="pmid">29423862</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR8">Shah DK, Loganzo F, Haddish-Berhane N et al (2018) Establishing in vitro-in vivo correlation for antibody drug conjugate efficacy: a PK/PD modeling approach. J Pharmacokinet Pharmacodyn 45:339–349<pub-id pub-id-type="pmid">29423862</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR9"><label>9.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR9"><person-group person-group-type="author"><name><surname>Morgan</surname><given-names>RA</given-names></name></person-group><article-title>Human tumor xenografts: the good, the bad, and the ugly</article-title><source>Mol Ther</source><year>2012</year><volume>20</volume><fpage>882</fpage><lpage>884</lpage><?supplied-pmid 22549804?><pub-id pub-id-type="pmid">22549804</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR9">Morgan RA (2012) Human tumor xenografts: the good, the bad, and the ugly. Mol Ther 20:882–884<pub-id pub-id-type="pmid">22549804</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR10"><label>10.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR10"><person-group person-group-type="author"><name><surname>Helmlinger</surname><given-names>G</given-names></name><name><surname>Sokolov</surname><given-names>V</given-names></name><name><surname>Peskov</surname><given-names>K</given-names></name><etal/></person-group><article-title>Quantitative systems pharmacology: an exemplar model-building workflow with applications in cardiovascular, metabolic, and oncology drug development</article-title><source>CPT Pharmacometrics Syst Pharmacol</source><year>2019</year><pub-id pub-id-type="doi">10.1002/psp4.12426</pub-id><?supplied-pmid 31087533?><pub-id pub-id-type="pmid">31087533</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR10">Helmlinger G, Sokolov V, Peskov K et al (2019) Quantitative systems pharmacology: an exemplar model-building workflow with applications in cardiovascular, metabolic, and oncology drug development. CPT Pharmacometrics Syst Pharmacol. 10.1002/psp4.12426<pub-id pub-id-type="pmid">31087533</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR11"><label>11.</label><mixed-citation publication-type="other">Sorger PK, Allerheiligen SRB, Abernethy DR et al (2011) Quantitative and systems pharmacology in the post-genomic era: new approaches to discovering drugs and understanding therapeutic mechanisms. An NIH white paper by the QSP workshop group. NIH, Bethesda, MD</mixed-citation></ref><ref id="CR12"><label>12.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR12"><person-group person-group-type="author"><name><surname>Hamblett</surname><given-names>KJ</given-names></name><name><surname>Jacob</surname><given-names>AP</given-names></name><name><surname>Gurgel</surname><given-names>JL</given-names></name><etal/></person-group><article-title>SLC46A3 is required to transport catabolites of noncleavable antibody Maytansine conjugates from the lysosome to the cytoplasm</article-title><source>Cancer Res</source><year>2015</year><volume>75</volume><fpage>5329</fpage><lpage>5340</lpage><?supplied-pmid 26631267?><pub-id pub-id-type="pmid">26631267</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR12">Hamblett KJ, Jacob AP, Gurgel JL et al (2015) SLC46A3 is required to transport catabolites of noncleavable antibody Maytansine conjugates from the lysosome to the cytoplasm. Cancer Res 75:5329–5340<pub-id pub-id-type="pmid">26631267</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR13"><label>13.</label><mixed-citation publication-type="other">Tomabechi R, Kishimoto H, Sato T et al (2022) SLC46A3 is a lysosomal proton-coupled steroid conjugate and bile acid transporter involved in transport of active catabolites of T-DM1. PNAS Nexus 1:gac063</mixed-citation></ref><ref id="CR14"><label>14.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR14"><person-group person-group-type="author"><name><surname>Austin</surname><given-names>CD</given-names></name><name><surname>De Mazière</surname><given-names>AM</given-names></name><name><surname>Pisacane</surname><given-names>PI</given-names></name><etal/></person-group><article-title>Endocytosis and sorting of ErbB2 and the site of action of cancer therapeutics trastuzumab and geldanamycin</article-title><source>Mol Biol Cell</source><year>2004</year><volume>15</volume><fpage>5268</fpage><lpage>5282</lpage><?supplied-pmid 15385631?><pub-id pub-id-type="pmid">15385631</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR14">Austin CD, De Mazière AM, Pisacane PI et al (2004) Endocytosis and sorting of ErbB2 and the site of action of cancer therapeutics trastuzumab and geldanamycin. Mol Biol Cell 15:5268–5282<pub-id pub-id-type="pmid">15385631</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR15"><label>15.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR15"><person-group person-group-type="author"><name><surname>Li</surname><given-names>JY</given-names></name><name><surname>Perry</surname><given-names>SR</given-names></name><name><surname>Muniz-Medina</surname><given-names>V</given-names></name><etal/></person-group><article-title>A biparatopic HER2-targeting antibody-drug conjugate induces tumor regression in primary models refractory to or ineligible for HER2-targeted therapy</article-title><source>Cancer Cell</source><year>2016</year><volume>29</volume><fpage>117</fpage><lpage>129</lpage><?supplied-pmid 26766593?><pub-id pub-id-type="pmid">26766593</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR15">Li JY, Perry SR, Muniz-Medina V et al (2016) A biparatopic HER2-targeting antibody-drug conjugate induces tumor regression in primary models refractory to or ineligible for HER2-targeted therapy. Cancer Cell 29:117–129<pub-id pub-id-type="pmid">26766593</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR16"><label>16.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR16"><person-group person-group-type="author"><name><surname>Erickson</surname><given-names>HK</given-names></name><name><surname>Lewis Phillips</surname><given-names>GD</given-names></name><name><surname>Leipold</surname><given-names>DD</given-names></name><etal/></person-group><article-title>The effect of different linkers on target cell catabolism and pharmacokinetics/pharmacodynamics of trastuzumab maytansinoid conjugates</article-title><source>Mol Cancer Ther</source><year>2012</year><volume>11</volume><fpage>1133</fpage><lpage>1142</lpage><?supplied-pmid 22408268?><pub-id pub-id-type="pmid">22408268</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR16">Erickson HK, Lewis Phillips GD, Leipold DD et al (2012) The effect of different linkers on target cell catabolism and pharmacokinetics/pharmacodynamics of trastuzumab maytansinoid conjugates. Mol Cancer Ther 11:1133–1142<pub-id pub-id-type="pmid">22408268</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR17"><label>17.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR17"><person-group person-group-type="author"><name><surname>Lewis Phillips</surname><given-names>G</given-names></name><name><surname>Guo</surname><given-names>J</given-names></name><name><surname>Kiefer</surname><given-names>JR</given-names></name><etal/></person-group><article-title>Trastuzumab does not bind rat or mouse ErbB2/neu: implications for selection of non-clinical safety models for trastuzumab-based therapeutics</article-title><source>Breast Cancer Res Treat</source><year>2022</year><volume>191</volume><fpage>303</fpage><lpage>317</lpage><?supplied-pmid 34708303?><pub-id pub-id-type="pmid">34708303</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR17">Lewis Phillips G, Guo J, Kiefer JR et al (2022) Trastuzumab does not bind rat or mouse ErbB2/neu: implications for selection of non-clinical safety models for trastuzumab-based therapeutics. Breast Cancer Res Treat 191:303–317<pub-id pub-id-type="pmid">34708303</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR18"><label>18.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR18"><person-group person-group-type="author"><name><surname>Schmidt</surname><given-names>MM</given-names></name><name><surname>Wittrup</surname><given-names>KD</given-names></name></person-group><article-title>A modeling analysis of the effects of molecular size and binding affinity on tumor targeting</article-title><source>Mol Cancer Ther</source><year>2009</year><volume>8</volume><fpage>2861</fpage><lpage>2871</lpage><?supplied-pmid 19825804?><pub-id pub-id-type="pmid">19825804</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR18">Schmidt MM, Wittrup KD (2009) A modeling analysis of the effects of molecular size and binding affinity on tumor targeting. Mol Cancer Ther 8:2861–2871<pub-id pub-id-type="pmid">19825804</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR19"><label>19.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR19"><person-group person-group-type="author"><name><surname>Thurber</surname><given-names>GM</given-names></name><name><surname>Schmidt</surname><given-names>MM</given-names></name><name><surname>Wittrup</surname><given-names>KD</given-names></name></person-group><article-title>Factors determining antibody distribution in tumors</article-title><source>Trends Pharmacol Sci</source><year>2008</year><volume>29</volume><fpage>57</fpage><lpage>61</lpage><?supplied-pmid 18179828?><pub-id pub-id-type="pmid">18179828</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR19">Thurber GM, Schmidt MM, Wittrup KD (2008) Factors determining antibody distribution in tumors. Trends Pharmacol Sci 29:57–61<pub-id pub-id-type="pmid">18179828</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR20"><label>20.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR20"><person-group person-group-type="author"><name><surname>Thurber</surname><given-names>GM</given-names></name><name><surname>Schmidt</surname><given-names>MM</given-names></name><name><surname>Wittrup</surname><given-names>KD</given-names></name></person-group><article-title>Antibody tumor penetration: transport opposed by systemic and antigen-mediated clearance</article-title><source>Adv Drug Deliv Rev</source><year>2008</year><volume>60</volume><fpage>1421</fpage><lpage>1434</lpage><?supplied-pmid 18541331?><pub-id pub-id-type="pmid">18541331</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR20">Thurber GM, Schmidt MM, Wittrup KD (2008) Antibody tumor penetration: transport opposed by systemic and antigen-mediated clearance. Adv Drug Deliv Rev 60:1421–1434<pub-id pub-id-type="pmid">18541331</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR21"><label>21.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR21"><person-group person-group-type="author"><name><surname>Shah</surname><given-names>DK</given-names></name><name><surname>Betts</surname><given-names>AM</given-names></name></person-group><article-title>Towards a platform PBPK model to characterize the plasma and tissue disposition of monoclonal antibodies in preclinical species and human</article-title><source>J Pharmacokinet Pharmacodyn</source><year>2012</year><volume>39</volume><fpage>67</fpage><lpage>86</lpage><?supplied-pmid 22143261?><pub-id pub-id-type="pmid">22143261</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR21">Shah DK, Betts AM (2012) Towards a platform PBPK model to characterize the plasma and tissue disposition of monoclonal antibodies in preclinical species and human. J Pharmacokinet Pharmacodyn 39:67–86<pub-id pub-id-type="pmid">22143261</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR22"><label>22.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR22"><person-group person-group-type="author"><name><surname>Okamoto</surname><given-names>H</given-names></name><name><surname>Oitate</surname><given-names>M</given-names></name><name><surname>Hagihara</surname><given-names>K</given-names></name><etal/></person-group><article-title>Pharmacokinetics of trastuzumab deruxtecan (T-DXd), a novel anti-HER2 antibody-drug conjugate, in HER2-positive tumour-bearing mice</article-title><source>Xenobiotica</source><year>2020</year><volume>50</volume><fpage>1</fpage><lpage>9</lpage><pub-id pub-id-type="pmid">31625424</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR22">Okamoto H, Oitate M, Hagihara K et al (2020) Pharmacokinetics of trastuzumab deruxtecan (T-DXd), a novel anti-HER2 antibody-drug conjugate, in HER2-positive tumour-bearing mice. Xenobiotica 50:1–9<pub-id pub-id-type="pmid">31625424</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR23"><label>23.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR23"><person-group person-group-type="author"><name><surname>Xie</surname><given-names>H</given-names></name><name><surname>Audette</surname><given-names>C</given-names></name><name><surname>Hoffee</surname><given-names>M</given-names></name><etal/></person-group><article-title>Pharmacokinetics and biodistribution of the antitumor immunoconjugate, cantuzumab mertansine (huC242-DM1), and its two components in mice</article-title><source>J Pharmacol Exp Ther</source><year>2004</year><volume>308</volume><fpage>1073</fpage><lpage>1082</lpage><?supplied-pmid 14634038?><pub-id pub-id-type="pmid">14634038</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR23">Xie H, Audette C, Hoffee M et al (2004) Pharmacokinetics and biodistribution of the antitumor immunoconjugate, cantuzumab mertansine (huC242-DM1), and its two components in mice. J Pharmacol Exp Ther 308:1073–1082<pub-id pub-id-type="pmid">14634038</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR24"><label>24.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR24"><person-group person-group-type="author"><name><surname>Haddish-Berhane</surname><given-names>N</given-names></name><name><surname>Shah</surname><given-names>DK</given-names></name><name><surname>Ma</surname><given-names>D</given-names></name><etal/></person-group><article-title>On translation of antibody drug conjugates efficacy from mouse experimental tumors to the clinic: a PK/PD approach</article-title><source>J Pharmacokinet Pharmacodyn</source><year>2013</year><volume>40</volume><fpage>557</fpage><lpage>571</lpage><?supplied-pmid 23933716?><pub-id pub-id-type="pmid">23933716</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR24">Haddish-Berhane N, Shah DK, Ma D et al (2013) On translation of antibody drug conjugates efficacy from mouse experimental tumors to the clinic: a PK/PD approach. J Pharmacokinet Pharmacodyn 40:557–571<pub-id pub-id-type="pmid">23933716</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR25"><label>25.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR25"><person-group person-group-type="author"><name><surname>Simeoni</surname><given-names>M</given-names></name><name><surname>Magni</surname><given-names>P</given-names></name><name><surname>Cammia</surname><given-names>C</given-names></name><etal/></person-group><article-title>Predictive pharmacokinetic-pharmacodynamic modeling of tumor growth kinetics in xenograft models after administration of anticancer agents</article-title><source>Cancer Res</source><year>2004</year><volume>64</volume><fpage>1094</fpage><lpage>1101</lpage><?supplied-pmid 14871843?><pub-id pub-id-type="pmid">14871843</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR25">Simeoni M, Magni P, Cammia C et al (2004) Predictive pharmacokinetic-pharmacodynamic modeling of tumor growth kinetics in xenograft models after administration of anticancer agents. Cancer Res 64:1094–1101<pub-id pub-id-type="pmid">14871843</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR26"><label>26.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR26"><person-group person-group-type="author"><name><surname>Yamashita-Kashima</surname><given-names>Y</given-names></name><name><surname>Shu</surname><given-names>S</given-names></name><name><surname>Harada</surname><given-names>N</given-names></name><name><surname>Fujimoto-Ouchi</surname><given-names>K</given-names></name></person-group><article-title>Enhanced antitumor activity of trastuzumab emtansine (T-DM1) in combination with pertuzumab in a HER2-positive gastric cancer model</article-title><source>Oncol Rep</source><year>2013</year><volume>30</volume><fpage>1087</fpage><lpage>1093</lpage><?supplied-pmid 23783223?><pub-id pub-id-type="pmid">23783223</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR26">Yamashita-Kashima Y, Shu S, Harada N, Fujimoto-Ouchi K (2013) Enhanced antitumor activity of trastuzumab emtansine (T-DM1) in combination with pertuzumab in a HER2-positive gastric cancer model. Oncol Rep 30:1087–1093<pub-id pub-id-type="pmid">23783223</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR27"><label>27.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR27"><person-group person-group-type="author"><name><surname>van der Lee</surname><given-names>MMC</given-names></name><name><surname>Groothuis</surname><given-names>PG</given-names></name><name><surname>Ubink</surname><given-names>R</given-names></name><etal/></person-group><article-title>The preclinical profile of the duocarmycin-based HER2-targeting ADC SYD985 predicts for clinical benefit in low HER2-expressing breast cancers</article-title><source>Mol Cancer Ther</source><year>2015</year><volume>14</volume><fpage>692</fpage><lpage>703</lpage><?supplied-pmid 25589493?><pub-id pub-id-type="pmid">25589493</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR27">van der Lee MMC, Groothuis PG, Ubink R et al (2015) The preclinical profile of the duocarmycin-based HER2-targeting ADC SYD985 predicts for clinical benefit in low HER2-expressing breast cancers. Mol Cancer Ther 14:692–703<pub-id pub-id-type="pmid">25589493</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR28"><label>28.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR28"><person-group person-group-type="author"><name><surname>Lewis Phillips</surname><given-names>GD</given-names></name><name><surname>Li</surname><given-names>G</given-names></name><name><surname>Dugger</surname><given-names>DL</given-names></name><etal/></person-group><article-title>Targeting HER2-positive breast cancer with trastuzumab-DM1, an antibody-cytotoxic drug conjugate</article-title><source>Cancer Res</source><year>2008</year><volume>68</volume><fpage>9280</fpage><lpage>9290</lpage><?supplied-pmid 19010901?><pub-id pub-id-type="pmid">19010901</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR28">Lewis Phillips GD, Li G, Dugger DL et al (2008) Targeting HER2-positive breast cancer with trastuzumab-DM1, an antibody-cytotoxic drug conjugate. Cancer Res 68:9280–9290<pub-id pub-id-type="pmid">19010901</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR29"><label>29.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR29"><person-group person-group-type="author"><name><surname>Ogitani</surname><given-names>Y</given-names></name><name><surname>Aida</surname><given-names>T</given-names></name><name><surname>Hagihara</surname><given-names>K</given-names></name><etal/></person-group><article-title>DS-8201a, a novel HER2-targeting ADC with a novel DNA topoisomerase I inhibitor, demonstrates a promising antitumor efficacy with differentiation from T-DM1</article-title><source>Clin Cancer Res</source><year>2016</year><volume>22</volume><fpage>5097</fpage><lpage>5108</lpage><?supplied-pmid 27026201?><pub-id pub-id-type="pmid">27026201</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR29">Ogitani Y, Aida T, Hagihara K et al (2016) DS-8201a, a novel HER2-targeting ADC with a novel DNA topoisomerase I inhibitor, demonstrates a promising antitumor efficacy with differentiation from T-DM1. Clin Cancer Res 22:5097–5108<pub-id pub-id-type="pmid">27026201</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR30"><label>30.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR30"><person-group person-group-type="author"><name><surname>Betts</surname><given-names>A</given-names></name><name><surname>Clark</surname><given-names>T</given-names></name><name><surname>Jasper</surname><given-names>P</given-names></name><etal/></person-group><article-title>Use of translational modeling and simulation for quantitative comparison of PF-06804103, a new generation HER2 ADC, with trastuzumab-DM1</article-title><source>J Pharmacokinet Pharmacodyn</source><year>2020</year><volume>47</volume><fpage>513</fpage><lpage>526</lpage><?supplied-pmid 32710210?><pub-id pub-id-type="pmid">32710210</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR30">Betts A, Clark T, Jasper P et al (2020) Use of translational modeling and simulation for quantitative comparison of PF-06804103, a new generation HER2 ADC, with trastuzumab-DM1. J Pharmacokinet Pharmacodyn 47:513–526<pub-id pub-id-type="pmid">32710210</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR31"><label>31.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR31"><person-group person-group-type="author"><name><surname>Girish</surname><given-names>S</given-names></name><name><surname>Gupta</surname><given-names>M</given-names></name><name><surname>Wang</surname><given-names>B</given-names></name><etal/></person-group><article-title>Clinical pharmacology of trastuzumab emtansine (T-DM1): an antibody-drug conjugate in development for the treatment of HER2-positive cancer</article-title><source>Cancer Chemother Pharmacol</source><year>2012</year><volume>69</volume><fpage>1229</fpage><lpage>1240</lpage><?supplied-pmid 22271209?><pub-id pub-id-type="pmid">22271209</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR31">Girish S, Gupta M, Wang B et al (2012) Clinical pharmacology of trastuzumab emtansine (T-DM1): an antibody-drug conjugate in development for the treatment of HER2-positive cancer. Cancer Chemother Pharmacol 69:1229–1240<pub-id pub-id-type="pmid">22271209</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR32"><label>32.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR32"><person-group person-group-type="author"><name><surname>Doi</surname><given-names>T</given-names></name><name><surname>Shitara</surname><given-names>K</given-names></name><name><surname>Naito</surname><given-names>Y</given-names></name><etal/></person-group><article-title>Safety, pharmacokinetics, and antitumour activity of trastuzumab deruxtecan (DS-8201), a HER2-targeting antibody–drug conjugate, in patients with advanced breast and gastric or gastro-oesophageal tumours: a phase 1 dose-escalation study</article-title><source>Lancet Oncol</source><year>2017</year><volume>18</volume><fpage>1512</fpage><lpage>1522</lpage><?supplied-pmid 29037983?><pub-id pub-id-type="pmid">29037983</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR32">Doi T, Shitara K, Naito Y et al (2017) Safety, pharmacokinetics, and antitumour activity of trastuzumab deruxtecan (DS-8201), a HER2-targeting antibody–drug conjugate, in patients with advanced breast and gastric or gastro-oesophageal tumours: a phase 1 dose-escalation study. Lancet Oncol 18:1512–1522<pub-id pub-id-type="pmid">29037983</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR33"><label>33.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR33"><person-group person-group-type="author"><name><surname>Kay</surname><given-names>K</given-names></name><name><surname>Dolcy</surname><given-names>K</given-names></name><name><surname>Bies</surname><given-names>R</given-names></name><name><surname>Shah</surname><given-names>DK</given-names></name></person-group><article-title>Estimation of solid tumor doubling times from progression-free survival plots using a novel statistical approach</article-title><source>AAPS J</source><year>2019</year><volume>21</volume><fpage>27</fpage><?supplied-pmid 30737615?><pub-id pub-id-type="pmid">30737615</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR33">Kay K, Dolcy K, Bies R, Shah DK (2019) Estimation of solid tumor doubling times from progression-free survival plots using a novel statistical approach. AAPS J 21:27<pub-id pub-id-type="pmid">30737615</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR34"><label>34.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR34"><person-group person-group-type="author"><name><surname>Singh</surname><given-names>AP</given-names></name><name><surname>Shah</surname><given-names>DK</given-names></name></person-group><article-title>Application of a PK-PD modeling and simulation-based strategy for clinical translation of antibody-drug conjugates: a case study with trastuzumab emtansine (T-DM1)</article-title><source>AAPS J</source><year>2017</year><volume>19</volume><fpage>1054</fpage><lpage>1070</lpage><?supplied-pmid 28374319?><pub-id pub-id-type="pmid">28374319</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR34">Singh AP, Shah DK (2017) Application of a PK-PD modeling and simulation-based strategy for clinical translation of antibody-drug conjugates: a case study with trastuzumab emtansine (T-DM1). AAPS J 19:1054–1070<pub-id pub-id-type="pmid">28374319</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR35"><label>35.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR35"><person-group person-group-type="author"><name><surname>Onsum</surname><given-names>MD</given-names></name><name><surname>Geretti</surname><given-names>E</given-names></name><name><surname>Paragas</surname><given-names>V</given-names></name><etal/></person-group><article-title>Single-cell quantitative HER2 measurement identifies heterogeneity and distinct subgroups within traditionally defined HER2-positive patients</article-title><source>Am J Pathol</source><year>2013</year><volume>183</volume><fpage>1446</fpage><lpage>1460</lpage><?supplied-pmid 24035511?><pub-id pub-id-type="pmid">24035511</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR35">Onsum MD, Geretti E, Paragas V et al (2013) Single-cell quantitative HER2 measurement identifies heterogeneity and distinct subgroups within traditionally defined HER2-positive patients. Am J Pathol 183:1446–1460<pub-id pub-id-type="pmid">24035511</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR36"><label>36.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR36"><person-group person-group-type="author"><name><surname>Lee</surname><given-names>SH</given-names></name><name><surname>Kim</surname><given-names>Y-S</given-names></name><name><surname>Han</surname><given-names>W</given-names></name><etal/></person-group><article-title>Tumor growth rate of invasive breast cancers during wait times for surgery assessed by ultrasonography</article-title><source>Medicine</source><year>2016</year><volume>95</volume><fpage>e4874</fpage><?supplied-pmid 27631256?><pub-id pub-id-type="pmid">27631256</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR36">Lee SH, Kim Y-S, Han W et al (2016) Tumor growth rate of invasive breast cancers during wait times for surgery assessed by ultrasonography. Medicine 95:e4874<pub-id pub-id-type="pmid">27631256</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR37"><label>37.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR37"><person-group person-group-type="author"><name><surname>Spratt</surname><given-names>JA</given-names></name><name><surname>von Fournier</surname><given-names>D</given-names></name><name><surname>Spratt</surname><given-names>JS</given-names></name><name><surname>Weber</surname><given-names>EE</given-names></name></person-group><article-title>Mammographic assessment of human breast cancer growth and duration</article-title><source>Cancer</source><year>1993</year><volume>71</volume><fpage>2020</fpage><lpage>2026</lpage><?supplied-pmid 8443754?><pub-id pub-id-type="pmid">8443754</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR37">Spratt JA, von Fournier D, Spratt JS, Weber EE (1993) Mammographic assessment of human breast cancer growth and duration. Cancer 71:2020–2026<pub-id pub-id-type="pmid">8443754</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR38"><label>38.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR38"><person-group person-group-type="author"><name><surname>Marino</surname><given-names>S</given-names></name><name><surname>Hogue</surname><given-names>IB</given-names></name><name><surname>Ray</surname><given-names>CJ</given-names></name><name><surname>Kirschner</surname><given-names>DE</given-names></name></person-group><article-title>A methodology for performing global uncertainty and sensitivity analysis in systems biology</article-title><source>J Theor Biol</source><year>2008</year><volume>254</volume><fpage>178</fpage><lpage>196</lpage><?supplied-pmid 18572196?><pub-id pub-id-type="pmid">18572196</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR38">Marino S, Hogue IB, Ray CJ, Kirschner DE (2008) A methodology for performing global uncertainty and sensitivity analysis in systems biology. J Theor Biol 254:178–196<pub-id pub-id-type="pmid">18572196</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR39"><label>39.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR39"><person-group person-group-type="author"><name><surname>Burris</surname><given-names>HA</given-names><suffix>3rd</suffix></name><name><surname>Rugo</surname><given-names>HS</given-names></name><name><surname>Vukelja</surname><given-names>SJ</given-names></name><etal/></person-group><article-title>Phase II study of the antibody drug conjugate trastuzumab-DM1 for the treatment of human epidermal growth factor receptor 2 (HER2)-positive breast cancer after prior HER2-directed therapy</article-title><source>J Clin Oncol</source><year>2011</year><volume>29</volume><fpage>398</fpage><lpage>405</lpage><?supplied-pmid 21172893?><pub-id pub-id-type="pmid">21172893</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR39">Burris HA 3rd, Rugo HS, Vukelja SJ et al (2011) Phase II study of the antibody drug conjugate trastuzumab-DM1 for the treatment of human epidermal growth factor receptor 2 (HER2)-positive breast cancer after prior HER2-directed therapy. J Clin Oncol 29:398–405<pub-id pub-id-type="pmid">21172893</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR40"><label>40.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR40"><person-group person-group-type="author"><name><surname>Modi</surname><given-names>S</given-names></name><name><surname>Saura</surname><given-names>C</given-names></name><name><surname>Yamashita</surname><given-names>T</given-names></name><etal/></person-group><article-title>Trastuzumab deruxtecan in previously treated HER2-positive breast cancer</article-title><source>N Engl J Med</source><year>2020</year><volume>382</volume><fpage>610</fpage><lpage>621</lpage><?supplied-pmid 31825192?><pub-id pub-id-type="pmid">31825192</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR40">Modi S, Saura C, Yamashita T et al (2020) Trastuzumab deruxtecan in previously treated HER2-positive breast cancer. N Engl J Med 382:610–621<pub-id pub-id-type="pmid">31825192</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR41"><label>41.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR41"><person-group person-group-type="author"><name><surname>Modi</surname><given-names>S</given-names></name><name><surname>Jacot</surname><given-names>W</given-names></name><name><surname>Yamashita</surname><given-names>T</given-names></name><etal/></person-group><article-title>Trastuzumab deruxtecan in previously treated HER2-low advanced breast cancer</article-title><source>N Engl J Med</source><year>2022</year><volume>387</volume><fpage>9</fpage><lpage>20</lpage><?supplied-pmid 35665782?><pub-id pub-id-type="pmid">35665782</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR41">Modi S, Jacot W, Yamashita T et al (2022) Trastuzumab deruxtecan in previously treated HER2-low advanced breast cancer. N Engl J Med 387:9–20<pub-id pub-id-type="pmid">35665782</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR42"><label>42.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR42"><person-group person-group-type="author"><name><surname>Eisenhauer</surname><given-names>EA</given-names></name><name><surname>Therasse</surname><given-names>P</given-names></name><name><surname>Bogaerts</surname><given-names>J</given-names></name><etal/></person-group><article-title>New response evaluation criteria in solid tumours: revised RECIST guideline (version 1.1)</article-title><source>Eur J Cancer</source><year>2009</year><volume>45</volume><fpage>228</fpage><lpage>247</lpage><?supplied-pmid 19097774?><pub-id pub-id-type="pmid">19097774</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR42">Eisenhauer EA, Therasse P, Bogaerts J et al (2009) New response evaluation criteria in solid tumours: revised RECIST guideline (version 1.1). Eur J Cancer 45:228–247<pub-id pub-id-type="pmid">19097774</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR43"><label>43.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR43"><person-group person-group-type="author"><name><surname>Rich</surname><given-names>JT</given-names></name><name><surname>Neely</surname><given-names>JG</given-names></name><name><surname>Paniello</surname><given-names>RC</given-names></name><etal/></person-group><article-title>A practical guide to understanding Kaplan-Meier curves</article-title><source>Otolaryngol Head Neck Surg</source><year>2010</year><volume>143</volume><fpage>331</fpage><lpage>336</lpage><?supplied-pmid 20723767?><pub-id pub-id-type="pmid">20723767</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR43">Rich JT, Neely JG, Paniello RC et al (2010) A practical guide to understanding Kaplan-Meier curves. Otolaryngol Head Neck Surg 143:331–336<pub-id pub-id-type="pmid">20723767</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR44"><label>44.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR44"><person-group person-group-type="author"><name><surname>Hunter</surname><given-names>JD</given-names></name></person-group><article-title>Matplotlib: a 2D graphics environment</article-title><source>Comput Sci Eng</source><year>2007</year><volume>9</volume><fpage>90</fpage><lpage>95</lpage></element-citation><mixed-citation publication-type="journal" id="mc-CR44">Hunter JD (2007) Matplotlib: a 2D graphics environment. Comput Sci Eng 9:90–95</mixed-citation></citation-alternatives></ref><ref id="CR45"><label>45.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR45"><person-group person-group-type="author"><name><surname>Drago</surname><given-names>JZ</given-names></name><name><surname>Modi</surname><given-names>S</given-names></name><name><surname>Chandarlapaty</surname><given-names>S</given-names></name></person-group><article-title>Unlocking the potential of antibody-drug conjugates for cancer therapy</article-title><source>Nat Rev Clin Oncol</source><year>2021</year><volume>18</volume><fpage>327</fpage><lpage>344</lpage><?supplied-pmid 33558752?><pub-id pub-id-type="pmid">33558752</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR45">Drago JZ, Modi S, Chandarlapaty S (2021) Unlocking the potential of antibody-drug conjugates for cancer therapy. Nat Rev Clin Oncol 18:327–344<pub-id pub-id-type="pmid">33558752</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR46"><label>46.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR46"><person-group person-group-type="author"><name><surname>Zhu</surname><given-names>AZ</given-names></name></person-group><article-title>Quantitative translational modeling to facilitate preclinical to clinical efficacy &amp; toxicity translation in oncology</article-title><source>Fut Sci OA</source><year>2018</year><volume>4</volume><fpage>FSO306</fpage></element-citation><mixed-citation publication-type="journal" id="mc-CR46">Zhu AZ (2018) Quantitative translational modeling to facilitate preclinical to clinical efficacy &amp; toxicity translation in oncology. Fut Sci OA 4:FSO306</mixed-citation></citation-alternatives></ref><ref id="CR47"><label>47.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR47"><person-group person-group-type="author"><name><surname>Jumbe</surname><given-names>NL</given-names></name><name><surname>Xin</surname><given-names>Y</given-names></name><name><surname>Leipold</surname><given-names>DD</given-names></name><etal/></person-group><article-title>Modeling the efficacy of trastuzumab-DM1, an antibody drug conjugate, in mice</article-title><source>J Pharmacokinet Pharmacodyn</source><year>2010</year><volume>37</volume><fpage>221</fpage><lpage>242</lpage><?supplied-pmid 20424896?><pub-id pub-id-type="pmid">20424896</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR47">Jumbe NL, Xin Y, Leipold DD et al (2010) Modeling the efficacy of trastuzumab-DM1, an antibody drug conjugate, in mice. J Pharmacokinet Pharmacodyn 37:221–242<pub-id pub-id-type="pmid">20424896</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR48"><label>48.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR48"><person-group person-group-type="author"><name><surname>Sayama</surname><given-names>H</given-names></name><name><surname>Nagasaka</surname><given-names>Y</given-names></name><name><surname>Tabata</surname><given-names>K</given-names></name></person-group><article-title>An introduction to QSP modeling for pharmacologists</article-title><source>Nihon Yakurigaku Zasshi</source><year>2019</year><volume>154</volume><fpage>143</fpage><lpage>150</lpage><?supplied-pmid 31527365?><pub-id pub-id-type="pmid">31527365</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR48">Sayama H, Nagasaka Y, Tabata K (2019) An introduction to QSP modeling for pharmacologists. Nihon Yakurigaku Zasshi 154:143–150<pub-id pub-id-type="pmid">31527365</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR49"><label>49.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR49"><person-group person-group-type="author"><name><surname>Shah</surname><given-names>DK</given-names></name><name><surname>Haddish-Berhane</surname><given-names>N</given-names></name><name><surname>Betts</surname><given-names>A</given-names></name></person-group><article-title>Bench to bedside translation of antibody drug conjugates using a multiscale mechanistic PK/PD model: a case study with brentuximab-vedotin</article-title><source>J Pharmacokinet Pharmacodyn</source><year>2012</year><volume>39</volume><fpage>643</fpage><lpage>659</lpage><?supplied-pmid 23151991?><pub-id pub-id-type="pmid">23151991</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR49">Shah DK, Haddish-Berhane N, Betts A (2012) Bench to bedside translation of antibody drug conjugates using a multiscale mechanistic PK/PD model: a case study with brentuximab-vedotin. J Pharmacokinet Pharmacodyn 39:643–659<pub-id pub-id-type="pmid">23151991</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR50"><label>50.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR50"><person-group person-group-type="author"><name><surname>Singh</surname><given-names>AP</given-names></name><name><surname>Maass</surname><given-names>KF</given-names></name><name><surname>Betts</surname><given-names>AM</given-names></name><etal/></person-group><article-title>Evolution of antibody-drug conjugate tumor disposition model to predict preclinical tumor pharmacokinetics of trastuzumab-emtansine (T-DM1)</article-title><source>AAPS J</source><year>2016</year><volume>18</volume><fpage>861</fpage><lpage>875</lpage><?supplied-pmid 27029797?><pub-id pub-id-type="pmid">27029797</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR50">Singh AP, Maass KF, Betts AM et al (2016) Evolution of antibody-drug conjugate tumor disposition model to predict preclinical tumor pharmacokinetics of trastuzumab-emtansine (T-DM1). AAPS J 18:861–875<pub-id pub-id-type="pmid">27029797</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR51"><label>51.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR51"><person-group person-group-type="author"><name><surname>Betts</surname><given-names>AM</given-names></name><name><surname>Haddish-Berhane</surname><given-names>N</given-names></name><name><surname>Tolsma</surname><given-names>J</given-names></name><etal/></person-group><article-title>Preclinical to clinical translation of antibody-drug conjugates using PK/PD modeling: a retrospective analysis of inotuzumab ozogamicin</article-title><source>AAPS J</source><year>2016</year><volume>18</volume><fpage>1101</fpage><lpage>1116</lpage><?supplied-pmid 27198897?><pub-id pub-id-type="pmid">27198897</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR51">Betts AM, Haddish-Berhane N, Tolsma J et al (2016) Preclinical to clinical translation of antibody-drug conjugates using PK/PD modeling: a retrospective analysis of inotuzumab ozogamicin. AAPS J 18:1101–1116<pub-id pub-id-type="pmid">27198897</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR52"><label>52.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR52"><person-group person-group-type="author"><name><surname>Cilliers</surname><given-names>C</given-names></name><name><surname>Menezes</surname><given-names>B</given-names></name><name><surname>Nessler</surname><given-names>I</given-names></name><etal/></person-group><article-title>Improved tumor penetration and single-cell targeting of antibody-drug conjugates increases anticancer efficacy and host survival</article-title><source>Cancer Res</source><year>2018</year><volume>78</volume><fpage>758</fpage><lpage>768</lpage><?supplied-pmid 29217763?><pub-id pub-id-type="pmid">29217763</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR52">Cilliers C, Menezes B, Nessler I et al (2018) Improved tumor penetration and single-cell targeting of antibody-drug conjugates increases anticancer efficacy and host survival. Cancer Res 78:758–768<pub-id pub-id-type="pmid">29217763</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR53"><label>53.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR53"><person-group person-group-type="author"><name><surname>Menezes</surname><given-names>B</given-names></name><name><surname>Cilliers</surname><given-names>C</given-names></name><name><surname>Wessler</surname><given-names>T</given-names></name><etal/></person-group><article-title>An agent-based systems pharmacology model of the antibody-drug conjugate Kadcyla to predict efficacy of different dosing regimens</article-title><source>AAPS J</source><year>2020</year><volume>22</volume><fpage>29</fpage><?supplied-pmid 31942650?><pub-id pub-id-type="pmid">31942650</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR53">Menezes B, Cilliers C, Wessler T et al (2020) An agent-based systems pharmacology model of the antibody-drug conjugate Kadcyla to predict efficacy of different dosing regimens. AAPS J 22:29<pub-id pub-id-type="pmid">31942650</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR54"><label>54.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR54"><person-group person-group-type="author"><name><surname>Khera</surname><given-names>E</given-names></name><name><surname>Cilliers</surname><given-names>C</given-names></name><name><surname>Bhatnagar</surname><given-names>S</given-names></name><name><surname>Thurber</surname><given-names>GM</given-names></name></person-group><article-title>Computational transport analysis of antibody-drug conjugate bystander effects and payload tumoral distribution: implications for therapy</article-title><source>Mol Syst Des Eng</source><year>2018</year><volume>3</volume><fpage>73</fpage><lpage>88</lpage></element-citation><mixed-citation publication-type="journal" id="mc-CR54">Khera E, Cilliers C, Bhatnagar S, Thurber GM (2018) Computational transport analysis of antibody-drug conjugate bystander effects and payload tumoral distribution: implications for therapy. Mol Syst Des Eng 3:73–88</mixed-citation></citation-alternatives></ref><ref id="CR55"><label>55.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR55"><person-group person-group-type="author"><name><surname>Ait-Oudhia</surname><given-names>S</given-names></name><name><surname>Zhang</surname><given-names>W</given-names></name><name><surname>Mager</surname><given-names>DE</given-names></name></person-group><article-title>A mechanism-based PK/PD model for hematological toxicities induced by antibody-drug conjugates</article-title><source>AAPS J</source><year>2017</year><pub-id pub-id-type="doi">10.1208/s12248-017-0113-5</pub-id><?supplied-pmid 28646408?><pub-id pub-id-type="pmid">28646408</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR55">Ait-Oudhia S, Zhang W, Mager DE (2017) A mechanism-based PK/PD model for hematological toxicities induced by antibody-drug conjugates. AAPS J. 10.1208/s12248-017-0113-5<pub-id pub-id-type="pmid">28646408</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR56"><label>56.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR56"><person-group person-group-type="author"><name><surname>Bender</surname><given-names>BC</given-names></name><name><surname>Schaedeli-Stark</surname><given-names>F</given-names></name><name><surname>Koch</surname><given-names>R</given-names></name><etal/></person-group><article-title>A population pharmacokinetic/pharmacodynamic model of thrombocytopenia characterizing the effect of trastuzumab emtansine (T-DM1) on platelet counts in patients with HER2-positive metastatic breast cancer</article-title><source>Cancer Chemother Pharmacol</source><year>2012</year><volume>70</volume><fpage>591</fpage><lpage>601</lpage><?supplied-pmid 22886072?><pub-id pub-id-type="pmid">22886072</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR56">Bender BC, Schaedeli-Stark F, Koch R et al (2012) A population pharmacokinetic/pharmacodynamic model of thrombocytopenia characterizing the effect of trastuzumab emtansine (T-DM1) on platelet counts in patients with HER2-positive metastatic breast cancer. Cancer Chemother Pharmacol 70:591–601<pub-id pub-id-type="pmid">22886072</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR57"><label>57.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR57"><person-group person-group-type="author"><name><surname>Musante</surname><given-names>CJ</given-names></name><name><surname>Ramanujan</surname><given-names>S</given-names></name><name><surname>Schmidt</surname><given-names>BJ</given-names></name><etal/></person-group><article-title>Quantitative systems pharmacology: a case for disease models</article-title><source>Clin Pharmacol Ther</source><year>2017</year><volume>101</volume><fpage>24</fpage><lpage>27</lpage><?supplied-pmid 27709613?><pub-id pub-id-type="pmid">27709613</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR57">Musante CJ, Ramanujan S, Schmidt BJ et al (2017) Quantitative systems pharmacology: a case for disease models. Clin Pharmacol Ther 101:24–27<pub-id pub-id-type="pmid">27709613</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR58"><label>58.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR58"><person-group person-group-type="author"><name><surname>Hendriks</surname><given-names>BS</given-names></name><name><surname>Wiley</surname><given-names>HS</given-names></name><name><surname>Lauffenburger</surname><given-names>D</given-names></name></person-group><article-title>HER2-mediated effects on EGFR endosomal sorting: analysis of biophysical mechanisms</article-title><source>Biophys J</source><year>2003</year><volume>85</volume><fpage>2732</fpage><lpage>2745</lpage><?supplied-pmid 14507736?><pub-id pub-id-type="pmid">14507736</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR58">Hendriks BS, Wiley HS, Lauffenburger D (2003) HER2-mediated effects on EGFR endosomal sorting: analysis of biophysical mechanisms. Biophys J 85:2732–2745<pub-id pub-id-type="pmid">14507736</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR59"><label>59.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR59"><person-group person-group-type="author"><name><surname>Erickson</surname><given-names>HK</given-names></name><name><surname>Park</surname><given-names>PU</given-names></name><name><surname>Widdison</surname><given-names>WC</given-names></name><etal/></person-group><article-title>Antibody-maytansinoid conjugates are activated in targeted cancer cells by lysosomal degradation and linker-dependent intracellular processing</article-title><source>Cancer Res</source><year>2006</year><volume>66</volume><fpage>4426</fpage><lpage>4433</lpage><?supplied-pmid 16618769?><pub-id pub-id-type="pmid">16618769</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR59">Erickson HK, Park PU, Widdison WC et al (2006) Antibody-maytansinoid conjugates are activated in targeted cancer cells by lysosomal degradation and linker-dependent intracellular processing. Cancer Res 66:4426–4433<pub-id pub-id-type="pmid">16618769</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR60"><label>60.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR60"><person-group person-group-type="author"><name><surname>Barok</surname><given-names>M</given-names></name><name><surname>Joensuu</surname><given-names>H</given-names></name><name><surname>Isola</surname><given-names>J</given-names></name></person-group><article-title>Trastuzumab emtansine: mechanisms of action and drug resistance</article-title><source>Breast Cancer Res</source><year>2014</year><volume>16</volume><fpage>209</fpage><?supplied-pmid 24887180?><pub-id pub-id-type="pmid">24887180</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR60">Barok M, Joensuu H, Isola J (2014) Trastuzumab emtansine: mechanisms of action and drug resistance. Breast Cancer Res 16:209<pub-id pub-id-type="pmid">24887180</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR61"><label>61.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR61"><person-group person-group-type="author"><name><surname>Nakada</surname><given-names>T</given-names></name><name><surname>Sugihara</surname><given-names>K</given-names></name><name><surname>Jikoh</surname><given-names>T</given-names></name><etal/></person-group><article-title>The latest research and development into the antibody-drug conjugate, [fam-] trastuzumab deruxtecan (DS-8201a), for HER2 cancer therapy</article-title><source>Chem Pharm Bull</source><year>2019</year><volume>67</volume><fpage>173</fpage><lpage>185</lpage></element-citation><mixed-citation publication-type="journal" id="mc-CR61">Nakada T, Sugihara K, Jikoh T et al (2019) The latest research and development into the antibody-drug conjugate, [fam-] trastuzumab deruxtecan (DS-8201a), for HER2 cancer therapy. Chem Pharm Bull 67:173–185</mixed-citation></citation-alternatives></ref><ref id="CR62"><label>62.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR62"><person-group person-group-type="author"><name><surname>Beaumont</surname><given-names>K</given-names></name><name><surname>Pike</surname><given-names>A</given-names></name><name><surname>Davies</surname><given-names>M</given-names></name><etal/></person-group><article-title>ADME and DMPK considerations for the discovery and development of antibody drug conjugates (ADCs)</article-title><source>Xenobiotica</source><year>2022</year><volume>52</volume><fpage>770</fpage><lpage>785</lpage><?supplied-pmid 36314242?><pub-id pub-id-type="pmid">36314242</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR62">Beaumont K, Pike A, Davies M et al (2022) ADME and DMPK considerations for the discovery and development of antibody drug conjugates (ADCs). Xenobiotica 52:770–785<pub-id pub-id-type="pmid">36314242</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR63"><label>63.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR63"><person-group person-group-type="author"><name><surname>Colombo</surname><given-names>R</given-names></name><name><surname>Rich</surname><given-names>JR</given-names></name></person-group><article-title>The therapeutic window of antibody drug conjugates: a dogma in need of revision</article-title><source>Cancer Cell</source><year>2022</year><volume>40</volume><fpage>1255</fpage><lpage>1263</lpage><?supplied-pmid 36240779?><pub-id pub-id-type="pmid">36240779</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR63">Colombo R, Rich JR (2022) The therapeutic window of antibody drug conjugates: a dogma in need of revision. Cancer Cell 40:1255–1263<pub-id pub-id-type="pmid">36240779</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR64"><label>64.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR64"><person-group person-group-type="author"><name><surname>Maass</surname><given-names>KF</given-names></name><name><surname>Kulkarni</surname><given-names>C</given-names></name><name><surname>Betts</surname><given-names>AM</given-names></name><name><surname>Wittrup</surname><given-names>KD</given-names></name></person-group><article-title>Determination of cellular processing rates for a trastuzumab-maytansinoid antibody-drug conjugate (ADC) highlights key parameters for ADC design</article-title><source>AAPS J</source><year>2016</year><volume>18</volume><fpage>635</fpage><lpage>646</lpage><?supplied-pmid 26912181?><pub-id pub-id-type="pmid">26912181</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR64">Maass KF, Kulkarni C, Betts AM, Wittrup KD (2016) Determination of cellular processing rates for a trastuzumab-maytansinoid antibody-drug conjugate (ADC) highlights key parameters for ADC design. AAPS J 18:635–646<pub-id pub-id-type="pmid">26912181</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR65"><label>65.</label><mixed-citation publication-type="other">Scheuher BM, Nowak M, McGirr K, et al (2021) Optimizing HER2-targeted ADC design using mechanistic modeling</mixed-citation></ref><ref id="CR66"><label>66.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR66"><person-group person-group-type="author"><name><surname>Shah</surname><given-names>DK</given-names></name><name><surname>King</surname><given-names>LE</given-names></name><name><surname>Han</surname><given-names>X</given-names></name><etal/></person-group><article-title>A priori prediction of tumor payload concentrations: preclinical case study with an auristatin-based anti-5T4 antibody-drug conjugate</article-title><source>AAPS J</source><year>2014</year><volume>16</volume><fpage>452</fpage><lpage>463</lpage><?supplied-pmid 24578215?><pub-id pub-id-type="pmid">24578215</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR66">Shah DK, King LE, Han X et al (2014) A priori prediction of tumor payload concentrations: preclinical case study with an auristatin-based anti-5T4 antibody-drug conjugate. AAPS J 16:452–463<pub-id pub-id-type="pmid">24578215</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR67"><label>67.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR67"><person-group person-group-type="author"><name><surname>Goldmacher</surname><given-names>VS</given-names></name><name><surname>Audette</surname><given-names>CA</given-names></name><name><surname>Guan</surname><given-names>Y</given-names></name><etal/></person-group><article-title>High-affinity accumulation of a maytansinoid in cells via weak tubulin interaction</article-title><source>PLoS ONE</source><year>2015</year><volume>10</volume><fpage>e0117523</fpage><?supplied-pmid 25671541?><pub-id pub-id-type="pmid">25671541</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR67">Goldmacher VS, Audette CA, Guan Y et al (2015) High-affinity accumulation of a maytansinoid in cells via weak tubulin interaction. PLoS ONE 10:e0117523<pub-id pub-id-type="pmid">25671541</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR68"><label>68.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR68"><person-group person-group-type="author"><name><surname>Singh</surname><given-names>AP</given-names></name><name><surname>Sharma</surname><given-names>S</given-names></name><name><surname>Shah</surname><given-names>DK</given-names></name></person-group><article-title>Quantitative characterization of in vitro bystander effect of antibody-drug conjugates</article-title><source>J Pharmacokinet Pharmacodyn</source><year>2016</year><volume>43</volume><fpage>567</fpage><lpage>582</lpage><?supplied-pmid 27670282?><pub-id pub-id-type="pmid">27670282</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR68">Singh AP, Sharma S, Shah DK (2016) Quantitative characterization of in vitro bystander effect of antibody-drug conjugates. J Pharmacokinet Pharmacodyn 43:567–582<pub-id pub-id-type="pmid">27670282</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR69"><label>69.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR69"><person-group person-group-type="author"><name><surname>Shitara</surname><given-names>K</given-names></name><name><surname>Baba</surname><given-names>E</given-names></name><name><surname>Fujitani</surname><given-names>K</given-names></name><etal/></person-group><article-title>Discovery and development of trastuzumab deruxtecan and safety management for patients with HER2-positive gastric cancer</article-title><source>Gastric Cancer</source><year>2021</year><volume>24</volume><fpage>780</fpage><lpage>789</lpage><?supplied-pmid 33997928?><pub-id pub-id-type="pmid">33997928</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR69">Shitara K, Baba E, Fujitani K et al (2021) Discovery and development of trastuzumab deruxtecan and safety management for patients with HER2-positive gastric cancer. Gastric Cancer 24:780–789<pub-id pub-id-type="pmid">33997928</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR70"><label>70.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR70"><person-group person-group-type="author"><name><surname>Jensen</surname><given-names>BV</given-names></name><name><surname>Johansen</surname><given-names>JS</given-names></name><name><surname>Price</surname><given-names>PA</given-names></name></person-group><article-title>High levels of serum HER-2/neu and YKL-40 independently reflect aggressiveness of metastatic breast cancer</article-title><source>Clin Cancer Res</source><year>2003</year><volume>9</volume><fpage>4423</fpage><lpage>4434</lpage><?supplied-pmid 14555515?><pub-id pub-id-type="pmid">14555515</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR70">Jensen BV, Johansen JS, Price PA (2003) High levels of serum HER-2/neu and YKL-40 independently reflect aggressiveness of metastatic breast cancer. Clin Cancer Res 9:4423–4434<pub-id pub-id-type="pmid">14555515</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR71"><label>71.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR71"><person-group person-group-type="author"><name><surname>Ali</surname><given-names>SM</given-names></name><name><surname>Carney</surname><given-names>WP</given-names></name><name><surname>Esteva</surname><given-names>FJ</given-names></name><etal/></person-group><article-title>Serum HER-2/neu and relative resistance to trastuzumab-based therapy in patients with metastatic breast cancer</article-title><source>Cancer</source><year>2008</year><volume>113</volume><fpage>1294</fpage><lpage>1301</lpage><?supplied-pmid 18661530?><pub-id pub-id-type="pmid">18661530</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR71">Ali SM, Carney WP, Esteva FJ et al (2008) Serum HER-2/neu and relative resistance to trastuzumab-based therapy in patients with metastatic breast cancer. Cancer 113:1294–1301<pub-id pub-id-type="pmid">18661530</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR72"><label>72.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR72"><person-group person-group-type="author"><name><surname>Baselga</surname><given-names>J</given-names></name></person-group><article-title>Phase I and II clinical trials of trastuzumab</article-title><source>Ann Oncol</source><year>2001</year><volume>12</volume><issue>Suppl 1</issue><fpage>S49</fpage><lpage>55</lpage><?supplied-pmid 11521722?><pub-id pub-id-type="pmid">11521722</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR72">Baselga J (2001) Phase I and II clinical trials of trastuzumab. Ann Oncol 12(Suppl 1):S49-55<pub-id pub-id-type="pmid">11521722</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR73"><label>73.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR73"><person-group person-group-type="author"><name><surname>Donaghy</surname><given-names>H</given-names></name></person-group><article-title>Effects of antibody, drug and linker on the preclinical and clinical toxicities of antibody-drug conjugates</article-title><source>MAbs</source><year>2016</year><volume>8</volume><fpage>659</fpage><lpage>671</lpage><?supplied-pmid 27045800?><pub-id pub-id-type="pmid">27045800</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR73">Donaghy H (2016) Effects of antibody, drug and linker on the preclinical and clinical toxicities of antibody-drug conjugates. MAbs 8:659–671<pub-id pub-id-type="pmid">27045800</pub-id>
</mixed-citation></citation-alternatives></ref><ref id="CR74"><label>74.</label><citation-alternatives><element-citation publication-type="journal" id="ec-CR74"><person-group person-group-type="author"><name><surname>Tai</surname><given-names>Y-T</given-names></name><name><surname>Mayes</surname><given-names>PA</given-names></name><name><surname>Acharya</surname><given-names>C</given-names></name><etal/></person-group><article-title>Novel anti-B-cell maturation antigen antibody-drug conjugate (GSK2857916) selectively induces killing of multiple myeloma</article-title><source>Blood</source><year>2014</year><volume>123</volume><fpage>3128</fpage><lpage>3138</lpage><?supplied-pmid 24569262?><pub-id pub-id-type="pmid">24569262</pub-id>
</element-citation><mixed-citation publication-type="journal" id="mc-CR74">Tai Y-T, Mayes PA, Acharya C et al (2014) Novel anti-B-cell maturation antigen antibody-drug conjugate (GSK2857916) selectively induces killing of multiple myeloma. Blood 123:3128–3138<pub-id pub-id-type="pmid">24569262</pub-id>
</mixed-citation></citation-alternatives></ref></ref-list></back></article>