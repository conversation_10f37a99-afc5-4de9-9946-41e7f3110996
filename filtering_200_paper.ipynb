{"cells": [{"cell_type": "code", "execution_count": 2, "id": "f2afc72f", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os"]}, {"cell_type": "code", "execution_count": 3, "id": "86b2f516", "metadata": {}, "outputs": [], "source": ["paper_list = pd.read_excel(\"paper_200_list.xlsx\", sheet_name = \"paper_200\")"]}, {"cell_type": "code", "execution_count": 4, "id": "72753bac", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>old</th>\n", "      <th>type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>W1491690127</td>\n", "      <td>full_article</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>W1670498114</td>\n", "      <td>full_article</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>W1983386052</td>\n", "      <td>full_article</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>W2034382557</td>\n", "      <td>full_article</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>W2059265067</td>\n", "      <td>full_article</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           old          type\n", "0  W1491690127  full_article\n", "1  W1670498114  full_article\n", "2  W1983386052  full_article\n", "3  W2034382557  full_article\n", "4  W2059265067  full_article"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["paper_list.head()"]}, {"cell_type": "code", "execution_count": 5, "id": "72753bac-2", "metadata": {}, "outputs": [{"data": {"text/plain": ["199"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["len(paper_list['old'])"]}, {"cell_type": "code", "execution_count": 6, "id": "7cbd2049", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 Starting XML file organization process...\n", "📊 Total paper IDs to process: 199\n", "📅 Process started at: 2025-09-05 18:14:45\n", "============================================================\n", "🔍 Checking source directories...\n", "  ✅ ../articles: 768 XML files found\n", "  ✅ ../reviews: 439 XML files found\n", "\n", "📁 Target directory: c:\\Users\\<USER>\\OneDrive - AZCollaboration\\Documents\\results\\paper_200_runs\n", "============================================================\n", "\n", "🔄 Processing paper IDs...\n", "✅ Copied: W1491690127.xml from articles\n", "✅ Copied: W1670498114.xml from articles\n", "✅ Copied: W1983386052.xml from reviews\n", "✅ Copied: W2034382557.xml from articles\n", "✅ Copied: W2059265067.xml from articles\n", "✅ Copied: W2071163127.xml from articles\n", "✅ Copied: W2088635881.xml from articles\n", "✅ Copied: W2119870604.xml from articles\n", "✅ Copied: W2150244588.xml from reviews\n", "✅ Copied: W2597720565.xml from reviews\n", "✅ Copied: W2746254865.xml from articles\n", "✅ Copied: W2767336874.xml from reviews\n", "✅ Copied: W2778226119.xml from articles\n", "✅ Copied: W2784139266.xml from articles\n", "✅ Copied: W2800788936.xml from articles\n", "✅ Copied: W2886552214.xml from articles\n", "✅ Copied: W2886837533.xml from reviews\n", "✅ Copied: W2894199580.xml from articles\n", "✅ Copied: W2898306454.xml from reviews\n", "📈 Progress: 20/199 papers processed (10.1%)\n", "✅ Copied: W2907078722.xml from articles\n", "✅ Copied: W2911746982.xml from articles\n", "✅ Copied: W2913433668.xml from reviews\n", "✅ Copied: W2923147053.xml from reviews\n", "✅ Copied: W2969941297.xml from articles\n", "✅ Copied: W2977895894.xml from articles\n", "✅ Copied: W2987115036.xml from articles\n", "✅ Copied: W3001098362.xml from articles\n", "✅ Copied: W3033351565.xml from reviews\n", "✅ Copied: W3043062793.xml from articles\n", "✅ Copied: W3083782960.xml from articles\n", "✅ Copied: W3090364682.xml from articles\n", "✅ Copied: W3093537091.xml from articles\n", "✅ Copied: W3110630842.xml from articles\n", "✅ Copied: W3119692971.xml from articles\n", "✅ Copied: W3123971507.xml from articles\n", "✅ Copied: W3126400332.xml from articles\n", "✅ Copied: W3135170551.xml from articles\n", "✅ Copied: W3139514272.xml from articles\n", "✅ Copied: W3157341523.xml from articles\n", "📈 Progress: 40/199 papers processed (20.1%)\n", "✅ Copied: W3167756601.xml from articles\n", "✅ Copied: W3169380838.xml from articles\n", "✅ Copied: W3177941998.xml from articles\n", "✅ Copied: W3182715110.xml from reviews\n", "✅ Copied: W3195418414.xml from articles\n", "✅ Copied: W3201179913.xml from reviews\n", "✅ Copied: W4200625287.xml from articles\n", "✅ Copied: W4210421364.xml from articles\n", "✅ Copied: W4225380120.xml from articles\n", "✅ Copied: W4226097966.xml from articles\n", "✅ Copied: W4226170358.xml from articles\n", "✅ Copied: W4280631979.xml from articles\n", "✅ Copied: W4281623024.xml from articles\n", "✅ Copied: W4283659311.xml from articles\n", "✅ Copied: W4284897854.xml from articles\n", "✅ Copied: W4307230178.xml from articles\n", "✅ Copied: W4312117329.xml from articles\n", "✅ Copied: W4315631613.xml from reviews\n", "✅ Copied: W4316814847.xml from reviews\n", "✅ Copied: W4362561157.xml from articles\n", "📈 Progress: 60/199 papers processed (30.2%)\n", "✅ Copied: W4372336332.xml from articles\n", "✅ Copied: W4376959447.xml from articles\n", "✅ Copied: W4380272548.xml from articles\n", "✅ Copied: W4381839168.xml from articles\n", "✅ Copied: W4381943608.xml from reviews\n", "❌ No XML file found for W4385978315\n", "✅ Copied: W4386125532.xml from articles\n", "✅ Copied: W4386484293.xml from reviews\n", "✅ Copied: W4386861187.xml from articles\n", "✅ Copied: W4387189752.xml from articles\n", "✅ Copied: W4387230410.xml from articles\n", "✅ Copied: W4387939270.xml from reviews\n", "✅ Copied: W4388422409.xml from articles\n", "✅ Copied: W4388771441.xml from reviews\n", "✅ Copied: W4389113389.xml from articles\n", "✅ Copied: W4389882757.xml from articles\n", "✅ Copied: W4390577977.xml from reviews\n", "✅ Copied: W4390739402.xml from articles\n", "✅ Copied: W4390743855.xml from articles\n", "✅ Copied: W4390885493.xml from articles\n", "📈 Progress: 80/199 papers processed (40.2%)\n", "✅ Copied: W4391224140.xml from articles\n", "✅ Copied: W4391838941.xml from articles\n", "✅ Copied: W4392047826.xml from articles\n", "✅ Copied: W4392158917.xml from articles\n", "✅ Copied: W4392466679.xml from articles\n", "✅ Copied: W4392643092.xml from articles\n", "✅ Copied: W4393015437.xml from reviews\n", "✅ Copied: W4393229268.xml from articles\n", "✅ Copied: W4394883394.xml from articles\n", "✅ Copied: W4394953855.xml from articles\n", "✅ Copied: W4395049238.xml from articles\n", "✅ Copied: W4396659145.xml from articles\n", "✅ Copied: W4396807179.xml from articles\n", "✅ Copied: W4398201134.xml from articles\n", "✅ Copied: W4400124600.xml from articles\n", "✅ Copied: W4400522799.xml from articles\n", "✅ Copied: W4401586017.xml from articles\n", "✅ Copied: W4406661909.xml from reviews\n", "❌ No XML file found for W2314772071\n", "❌ No XML file found for W4376958886\n", "📈 Progress: 100/199 papers processed (50.3%)\n", "✅ Copied: W4387297107.xml from articles\n", "✅ Copied: W2746257012.xml from articles\n", "✅ Copied: W2948531293.xml from articles\n", "✅ Copied: W4387577280.xml from articles\n", "✅ Copied: W4391616786.xml from articles\n", "✅ Copied: W1866424446.xml from articles\n", "✅ Copied: W3215636124.xml from articles\n", "✅ Copied: W4282917798.xml from articles\n", "✅ Copied: W4317777676.xml from articles\n", "✅ Copied: W4394892537.xml from articles\n", "✅ Copied: W4403685726.xml from articles\n", "✅ Copied: W4404044296.xml from articles\n", "✅ Copied: W2790661732.xml from articles\n", "✅ Copied: W3127524404.xml from articles\n", "✅ Copied: W3158036060.xml from articles\n", "✅ Copied: W4310644025.xml from articles\n", "✅ Copied: W4365135124.xml from articles\n", "✅ Copied: W1556136319.xml from articles\n", "✅ Copied: W2883692867.xml from articles\n", "✅ Copied: W2898599991.xml from articles\n", "📈 Progress: 120/199 papers processed (60.3%)\n", "✅ Copied: W3157843388.xml from articles\n", "✅ Copied: W3189201755.xml from articles\n", "✅ Copied: W4406500393.xml from articles\n", "✅ Copied: W1587404401.xml from articles\n", "✅ Copied: W2892197301.xml from articles\n", "✅ Copied: W2913496723.xml from articles\n", "✅ Copied: W2945070634.xml from articles\n", "✅ Copied: W2955413137.xml from articles\n", "✅ Copied: W2967364098.xml from articles\n", "✅ Copied: W2982514436.xml from articles\n", "✅ Copied: W3039998283.xml from articles\n", "✅ Copied: W3100477197.xml from articles\n", "✅ Copied: W4214839991.xml from articles\n", "✅ Copied: W4236361077.xml from articles\n", "✅ Copied: W4385706603.xml from articles\n", "✅ Copied: W4391805157.xml from articles\n", "✅ Copied: W4394615828.xml from articles\n", "✅ Copied: W4402601882.xml from articles\n", "✅ Copied: W2031127086.xml from articles\n", "✅ Copied: W2804013256.xml from articles\n", "📈 Progress: 140/199 papers processed (70.4%)\n", "✅ Copied: W2948140451.xml from articles\n", "✅ Copied: W2964046006.xml from articles\n", "✅ Copied: W3002982587.xml from articles\n", "✅ Copied: W3009291261.xml from articles\n", "✅ Copied: W3011163789.xml from articles\n", "✅ Copied: W3014722766.xml from articles\n", "✅ Copied: W3146742349.xml from articles\n", "✅ Copied: W3158998856.xml from articles\n", "✅ Copied: W3185054844.xml from articles\n", "✅ Copied: W3186744398.xml from articles\n", "✅ Copied: W4293173685.xml from articles\n", "✅ Copied: W4323535430.xml from articles\n", "✅ Copied: W4386802553.xml from articles\n", "✅ Copied: W4387579755.xml from articles\n", "✅ Copied: W4394692912.xml from articles\n", "✅ Copied: W4398159275.xml from articles\n", "✅ Copied: W4401728730.xml from articles\n", "✅ Copied: W4405470614.xml from articles\n", "✅ Copied: W4406540775.xml from articles\n", "✅ Copied: W2260947622.xml from articles\n", "📈 Progress: 160/199 papers processed (80.4%)\n", "✅ Copied: W2736059953.xml from articles\n", "✅ Copied: W2753908872.xml from articles\n", "✅ Copied: W2802956075.xml from articles\n", "✅ Copied: W2808760242.xml from articles\n", "✅ Copied: W2946715953.xml from articles\n", "✅ Copied: W3017732155.xml from articles\n", "✅ Copied: W3021852777.xml from articles\n", "✅ Copied: W3029754491.xml from articles\n", "✅ Copied: W3035857124.xml from articles\n", "✅ Copied: W3113389205.xml from articles\n", "✅ Copied: W3160790009.xml from articles\n", "✅ Copied: W3188189762.xml from articles\n", "✅ Copied: W4200320481.xml from articles\n", "✅ Copied: W4220850214.xml from articles\n", "✅ Copied: W4293024953.xml from articles\n", "✅ Copied: W4309327225.xml from articles\n", "✅ Copied: W4367393991.xml from articles\n", "✅ Copied: W4376138812.xml from articles\n", "✅ Copied: W4383341814.xml from articles\n", "✅ Copied: W4385767057.xml from articles\n", "📈 Progress: 180/199 papers processed (90.5%)\n", "✅ Copied: W4386225980.xml from reviews\n", "✅ Copied: W2064424529.xml from reviews\n", "✅ Copied: W3035793666.xml from reviews\n", "✅ Copied: W4389074603.xml from reviews\n", "✅ Copied: W4400350261.xml from reviews\n", "✅ Copied: W3088485305.xml from reviews\n", "✅ Copied: W4311049903.xml from reviews\n", "✅ Copied: W3186786458.xml from reviews\n", "✅ Copied: W2019434845.xml from reviews\n", "✅ Copied: W2767645122.xml from reviews\n", "✅ Copied: W3095130904.xml from reviews\n", "✅ Copied: W3174250023.xml from reviews\n", "✅ Copied: W2982651299.xml from reviews\n", "✅ Copied: W3155523643.xml from reviews\n", "✅ Copied: W4306923174.xml from reviews\n", "✅ Copied: W4400079479.xml from reviews\n", "✅ Copied: W2033619005.xml from reviews\n", "✅ Copied: W2908115935.xml from reviews\n", "✅ Copied: W2994610571.xml from reviews\n", "📈 Progress: 199/199 papers processed (100.0%)\n", "✅ Copied: W3099316220.xml from reviews\n", "\n", "======================================================================\n", "🎯 XML FILE ORGANIZATION SUMMARY REPORT\n", "======================================================================\n", "📊 Total paper IDs processed: 199\n", "✅ Paper IDs with XML found: 196 (98.5%)\n", "📁 XML files successfully copied: 196\n", "❌ Paper IDs not found: 3 (1.5%)\n", "⚠️  Copy errors encountered: 0\n", "\n", "📂 Files copied by source directory:\n", "  📄 articles: 156 files\n", "  📄 reviews: 40 files\n", "\n", "⏱️  Process completed at: 2025-09-05 18:14:50\n", "📁 Target directory: c:\\Users\\<USER>\\OneDrive - AZCollaboration\\Documents\\results\\paper_200_runs\n", "\n", "❌ Missing XML files for 3 paper IDs:\n", "   1. W4385978315\n", "   2. W2314772071\n", "   3. W4376958886\n", "\n", "======================================================================\n", "🎉 Process completed successfully!\n", "======================================================================\n", "\n", "💾 Saving detailed reports...\n", "📄 Missing paper IDs saved to: missing_xml_files_20250905_181450.csv\n", "📊 Summary report saved to: xml_copy_summary_20250905_181450.csv\n", "\n", "✨ All reports saved successfully!\n", "📁 Check the current directory for detailed CSV reports.\n"]}], "source": ["import pandas as pd\n", "import os\n", "import shutil\n", "from pathlib import Path\n", "from datetime import datetime\n", "\n", "# Extract paper IDs from the paper_list DataFrame\n", "paper_ids = paper_list['old'].tolist()\n", "\n", "print(f\"🚀 Starting XML file organization process...\")\n", "print(f\"📊 Total paper IDs to process: {len(paper_ids)}\")\n", "print(f\"📅 Process started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(\"=\" * 60)\n", "\n", "# Define source directories (relative to workspace root)\n", "source_directories = [\"../articles\", \"../reviews\"]\n", "target_dir = \".\"\n", "\n", "# Verify source directories exist\n", "print(\"🔍 Checking source directories...\")\n", "for source_dir in source_directories:\n", "    if os.path.exists(source_dir):\n", "        xml_count = len([f for f in os.listdir(source_dir) if f.endswith('.xml')])\n", "        print(f\"  ✅ {source_dir}: {xml_count} XML files found\")\n", "    else:\n", "        print(f\"  ❌ {source_dir}: Directory not found\")\n", "\n", "print(\"\\n📁 Target directory:\", os.path.abspath(target_dir))\n", "print(\"=\" * 60)\n", "\n", "# Initialize counters and tracking\n", "xml_files_copied = 0\n", "paper_ids_found = 0\n", "paper_ids_not_found = []\n", "copy_errors = []\n", "files_by_source = {'../articles': 0, '../reviews': 0}\n", "\n", "# Process each paper ID with progress tracking\n", "print(\"\\n🔄 Processing paper IDs...\")\n", "for i, paper_id in enumerate(paper_ids, 1):\n", "    found_xml = False\n", "    \n", "    # Progress indicator every 20 papers\n", "    if i % 20 == 0 or i == len(paper_ids):\n", "        print(f\"📈 Progress: {i}/{len(paper_ids)} papers processed ({i/len(paper_ids)*100:.1f}%)\")\n", "    \n", "    # Search for XML files in source directories\n", "    for source_dir in source_directories:\n", "        if os.path.exists(source_dir):\n", "            xml_file_path = os.path.join(source_dir, f\"{paper_id}.xml\")\n", "            \n", "            if os.path.exists(xml_file_path):\n", "                target_file_path = os.path.join(target_dir, f\"{paper_id}.xml\")\n", "                \n", "                try:\n", "                    # Check if file already exists to avoid overwriting\n", "                    if os.path.exists(target_file_path):\n", "                        print(f\"⚠️  {paper_id}.xml already exists in target directory\")\n", "                    else:\n", "                        shutil.copy2(xml_file_path, target_file_path)\n", "                        print(f\"✅ Copied: {paper_id}.xml from {source_dir.replace('../', '')}\")\n", "                    \n", "                    xml_files_copied += 1\n", "                    files_by_source[source_dir] += 1\n", "                    found_xml = True\n", "                    break  # Found the XML, no need to check other directories\n", "                    \n", "                except Exception as e:\n", "                    error_msg = f\"Failed to copy {paper_id}.xml: {str(e)}\"\n", "                    print(f\"❌ {error_msg}\")\n", "                    copy_errors.append({'paper_id': paper_id, 'error': str(e)})\n", "    \n", "    if found_xml:\n", "        paper_ids_found += 1\n", "    else:\n", "        print(f\"❌ No XML file found for {paper_id}\")\n", "        paper_ids_not_found.append(paper_id)\n", "\n", "# Comprehensive Summary Report\n", "end_time = datetime.now()\n", "print(\"\\n\" + \"=\" * 70)\n", "print(\"🎯 XML FILE ORGANIZATION SUMMARY REPORT\")\n", "print(\"=\" * 70)\n", "print(f\"📊 Total paper IDs processed: {len(paper_ids)}\")\n", "print(f\"✅ Paper IDs with XML found: {paper_ids_found} ({paper_ids_found/len(paper_ids)*100:.1f}%)\")\n", "print(f\"📁 XML files successfully copied: {xml_files_copied}\")\n", "print(f\"❌ Paper IDs not found: {len(paper_ids_not_found)} ({len(paper_ids_not_found)/len(paper_ids)*100:.1f}%)\")\n", "print(f\"⚠️  Copy errors encountered: {len(copy_errors)}\")\n", "\n", "# Files by source breakdown\n", "print(f\"\\n📂 Files copied by source directory:\")\n", "for source, count in files_by_source.items():\n", "    print(f\"  📄 {source.replace('../', '')}: {count} files\")\n", "\n", "# Timing information\n", "duration = end_time - datetime.strptime(datetime.now().strftime('%Y-%m-%d %H:%M:%S'), '%Y-%m-%d %H:%M:%S')\n", "print(f\"\\n⏱️  Process completed at: {end_time.strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(f\"📁 Target directory: {os.path.abspath(target_dir)}\")\n", "\n", "# Display missing files (limited to first 20 for readability)\n", "if paper_ids_not_found:\n", "    print(f\"\\n❌ Missing XML files for {len(paper_ids_not_found)} paper IDs:\")\n", "    display_count = min(20, len(paper_ids_not_found))\n", "    for i, missing_id in enumerate(paper_ids_not_found[:display_count]):\n", "        print(f\"  {i+1:2d}. {missing_id}\")\n", "    if len(paper_ids_not_found) > 20:\n", "        print(f\"  ... and {len(paper_ids_not_found) - 20} more (see CSV file for complete list)\")\n", "\n", "# Display copy errors if any\n", "if copy_errors:\n", "    print(f\"\\n⚠️  Copy errors encountered:\")\n", "    for error in copy_errors[:5]:  # Show first 5 errors\n", "        print(f\"  - {error['paper_id']}: {error['error']}\")\n", "    if len(copy_errors) > 5:\n", "        print(f\"  ... and {len(copy_errors) - 5} more errors\")\n", "\n", "print(\"\\n\" + \"=\" * 70)\n", "print(\"🎉 Process completed successfully!\")\n", "print(\"=\" * 70)\n", "\n", "# Save detailed reports to files\n", "print(\"\\n💾 Saving detailed reports...\")\n", "\n", "# Save missing IDs to CSV\n", "if paper_ids_not_found:\n", "    missing_df = pd.DataFrame(paper_ids_not_found, columns=['missing_paper_id'])\n", "    missing_file = f\"missing_xml_files_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv\"\n", "    missing_df.to_csv(missing_file, index=False)\n", "    print(f\"📄 Missing paper IDs saved to: {missing_file}\")\n", "\n", "# Save copy errors to CSV if any\n", "if copy_errors:\n", "    errors_df = pd.DataFrame(copy_errors)\n", "    errors_file = f\"copy_errors_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv\"\n", "    errors_df.to_csv(errors_file, index=False)\n", "    print(f\"⚠️  Copy errors saved to: {errors_file}\")\n", "\n", "# Save comprehensive summary report\n", "summary_data = {\n", "    'total_paper_ids': len(paper_ids),\n", "    'paper_ids_found': paper_ids_found,\n", "    'xml_files_copied': xml_files_copied,\n", "    'paper_ids_not_found': len(paper_ids_not_found),\n", "    'copy_errors': len(copy_errors),\n", "    'success_rate_percent': round(paper_ids_found/len(paper_ids)*100, 2),\n", "    'files_from_articles': files_by_source['../articles'],\n", "    'files_from_reviews': files_by_source['../reviews'],\n", "    'process_timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "}\n", "\n", "summary_df = pd.DataFrame([summary_data])\n", "summary_file = f\"xml_copy_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv\"\n", "summary_df.to_csv(summary_file, index=False)\n", "print(f\"📊 Summary report saved to: {summary_file}\")\n", "\n", "print(f\"\\n✨ All reports saved successfully!\")\n", "print(f\"📁 Check the current directory for detailed CSV reports.\")"]}, {"cell_type": "code", "execution_count": 7, "id": "verification-cell", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Quick verification of copied XML files...\n", "==================================================\n", "📁 XML files found in target directory: 196\n", "\n", "📄 Sample of copied files (first 10):\n", "   1. W1491690127.xml (81.2 KB)\n", "   2. W1556136319.xml (66.2 KB)\n", "   3. W1587404401.xml (71.0 KB)\n", "   4. W1670498114.xml (84.9 KB)\n", "   5. W1866424446.xml (159.8 KB)\n", "   6. W1983386052.xml (91.9 KB)\n", "   7. W2019434845.xml (188.7 KB)\n", "   8. W2031127086.xml (117.3 KB)\n", "   9. W2033619005.xml (114.7 KB)\n", "  10. W2034382557.xml (83.7 KB)\n", "  ... and 186 more files\n", "\n", "💾 Total size of copied XML files: 24.45 MB\n", "\n", "✅ Verification completed!\n"]}], "source": ["# Quick verification of copied XML files\n", "print(\"🔍 Quick verification of copied XML files...\")\n", "print(\"=\" * 50)\n", "\n", "# Count XML files in current directory\n", "xml_files_in_target = [f for f in os.listdir('.') if f.endswith('.xml')]\n", "print(f\"📁 XML files found in target directory: {len(xml_files_in_target)}\")\n", "\n", "# Show first 10 files as sample\n", "if xml_files_in_target:\n", "    print(f\"\\n📄 Sample of copied files (first 10):\")\n", "    for i, filename in enumerate(xml_files_in_target[:10], 1):\n", "        file_size = os.path.getsize(filename) / 1024  # Size in KB\n", "        print(f\"  {i:2d}. {filename} ({file_size:.1f} KB)\")\n", "    \n", "    if len(xml_files_in_target) > 10:\n", "        print(f\"  ... and {len(xml_files_in_target) - 10} more files\")\n", "    \n", "    # Calculate total size\n", "    total_size_mb = sum(os.path.getsize(f) for f in xml_files_in_target if f.endswith('.xml')) / (1024 * 1024)\n", "    print(f\"\\n💾 Total size of copied XML files: {total_size_mb:.2f} MB\")\n", "else:\n", "    print(\"❌ No XML files found in target directory\")\n", "\n", "print(\"\\n✅ Verification completed!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}